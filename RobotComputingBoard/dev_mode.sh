#!/bin/bash

# 相机设备开发模式控制脚本
# 用于在本地开发和容器运行之间切换相机设备的使用权

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Docker容器是否运行
check_container() {
    if ! docker ps | grep -q "swarm"; then
        echo -e "${RED}错误: swarm容器未运行${NC}"
        echo "请先启动Docker容器: ./docker_run.sh"
        exit 1
    fi
}

# 检查相机节点状态
check_camera_status() {
    local camera_nodes=$(docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rosnode list 2>/dev/null | grep cam" 2>/dev/null || echo "")
    if [ -n "$camera_nodes" ]; then
        echo -e "${GREEN}容器相机节点运行中:${NC}"
        echo "$camera_nodes"
        return 0
    else
        echo -e "${YELLOW}容器相机节点未运行${NC}"
        return 1
    fi
}

# 检查相机设备占用情况
check_camera_devices() {
    echo -e "${BLUE}相机设备状态:${NC}"
    for i in 0 1 2 3; do
        if lsof /dev/cam$i 2>/dev/null | grep -q cam$i; then
            echo -e "  /dev/cam$i: ${RED}被占用${NC}"
        else
            echo -e "  /dev/cam$i: ${GREEN}可用${NC}"
        fi
    done
}

# 开发模式：停止容器相机节点
dev_mode() {
    echo -e "${YELLOW}切换到开发模式...${NC}"
    check_container
    
    # 停止相机节点
    echo "停止容器内的相机节点..."
    docker exec swarm pkill -f "usb_cam" 2>/dev/null || true
    
    # 等待进程完全停止
    sleep 2
    
    # 验证停止结果
    if check_camera_status; then
        echo -e "${RED}警告: 部分相机节点可能仍在运行${NC}"
    else
        echo -e "${GREEN}✓ 相机节点已停止${NC}"
    fi
    
    echo -e "${GREEN}✓ 开发模式已激活${NC}"
    echo -e "${BLUE}相机设备现在可用于本地开发${NC}"
    
    # 显示设备状态
    check_camera_devices
    
    echo ""
    echo -e "${YELLOW}本地开发示例:${NC}"
    echo "  python3 -c \"import cv2; cap = cv2.VideoCapture(0); print('相机可用:', cap.isOpened()); cap.release()\""
}

# 生产模式：启动容器相机节点
prod_mode() {
    echo -e "${YELLOW}切换到生产模式...${NC}"
    check_container
    
    # 先停止可能存在的相机节点
    docker exec swarm pkill -f "usb_cam" 2>/dev/null || true
    sleep 2
    
    # 启动相机节点
    echo "启动容器内的相机节点..."
    docker exec -d swarm bash -c "source /opt/ros/noetic/setup.bash && roslaunch usb_cam vswarm-quad-360p.launch" 2>/dev/null
    
    # 等待启动
    echo "等待相机节点启动..."
    sleep 8
    
    # 验证启动结果
    if check_camera_status; then
        echo -e "${GREEN}✓ 生产模式已激活${NC}"
        echo -e "${BLUE}容器相机节点正常运行${NC}"
        
        # 测试相机话题
        echo ""
        echo "测试相机话题..."
        for i in 0 1 2 3; do
            local hz=$(docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && timeout 3 rostopic hz /cam$i/image_raw 2>/dev/null | tail -1 | grep -o '[0-9]*\.[0-9]*'" 2>/dev/null || echo "0")
            if [ "$hz" != "0" ] && [ -n "$hz" ]; then
                echo -e "  /cam$i/image_raw: ${GREEN}${hz} Hz${NC}"
            else
                echo -e "  /cam$i/image_raw: ${RED}无数据${NC}"
            fi
        done
    else
        echo -e "${RED}✗ 相机节点启动失败${NC}"
        echo "请检查Docker容器状态和相机设备连接"
    fi
}

# 状态检查
status_mode() {
    echo -e "${BLUE}=== 系统状态检查 ===${NC}"
    
    # 检查容器状态
    if docker ps | grep -q "swarm"; then
        echo -e "Docker容器: ${GREEN}运行中${NC}"
    else
        echo -e "Docker容器: ${RED}未运行${NC}"
        return 1
    fi
    
    echo ""
    # 检查相机节点
    check_camera_status
    
    echo ""
    # 检查设备状态
    check_camera_devices
    
    echo ""
    # 检查ROS话题
    echo -e "${BLUE}ROS话题状态:${NC}"
    local topics=$(docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rostopic list 2>/dev/null | grep image_raw" 2>/dev/null || echo "")
    if [ -n "$topics" ]; then
        echo "$topics"
    else
        echo -e "${YELLOW}无相机话题${NC}"
    fi
}

# 快速测试本地相机访问
test_local() {
    echo -e "${BLUE}测试本地相机访问...${NC}"
    
    python3 -c "
import cv2
import sys

success_count = 0
for i in range(4):
    try:
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print(f'相机 {i}: ✓ 可访问 ({frame.shape[1]}x{frame.shape[0]})')
                success_count += 1
            else:
                print(f'相机 {i}: ✗ 无法读取数据')
        else:
            print(f'相机 {i}: ✗ 无法打开')
        cap.release()
    except Exception as e:
        print(f'相机 {i}: ✗ 错误 - {e}')

print(f'\\n总计: {success_count}/4 个相机可用')
" 2>/dev/null
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}相机设备开发模式控制脚本${NC}"
    echo ""
    echo -e "${YELLOW}用法:${NC} $0 {dev|prod|status|test|help}"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "  ${GREEN}dev${NC}     - 开发模式（停止容器相机，释放设备给本地开发）"
    echo -e "  ${GREEN}prod${NC}    - 生产模式（启动容器相机节点）"
    echo -e "  ${GREEN}status${NC}  - 检查当前状态（容器、节点、设备）"
    echo -e "  ${GREEN}test${NC}    - 测试本地相机访问"
    echo -e "  ${GREEN}help${NC}    - 显示此帮助信息"
    echo ""
    echo -e "${YELLOW}使用场景:${NC}"
    echo -e "  • 本地开发时: ${BLUE}./dev_mode.sh dev${NC}"
    echo -e "  • 开发完成后: ${BLUE}./dev_mode.sh prod${NC}"
    echo -e "  • 检查状态: ${BLUE}./dev_mode.sh status${NC}"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo -e "  • 确保Docker容器正在运行"
    echo -e "  • 开发模式会停止容器内的相机节点"
    echo -e "  • 生产模式会重新启动容器内的相机节点"
}

# 主程序
case "$1" in
    "dev")
        dev_mode
        ;;
    "prod")
        prod_mode
        ;;
    "status")
        status_mode
        ;;
    "test")
        test_local
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}错误: 无效的选项 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
