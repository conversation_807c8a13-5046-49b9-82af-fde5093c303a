<launch>
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(find vswarm_sim)/world/lab_106_noob.world" />
    <arg name="use_sim_time" value="true"/>
    <arg name="gui" value="true"/>
  </include>


  <node
    name="tf_footprint_base"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0 0 0 0 1 base_footprint world" />
  <node
    name="tf_foottoworld"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0 0 0 0 1 base_link base_footprint" />

  <node
    name="tf_odom"
    pkg="tf2_ros"
    type="static_transform_publisher"
    args="0 0 0.05 0 0 0 1 world odom_frame" />

  <node
    name="fake_joint_calibration"
    pkg="rostopic"
    type="rostopic"
    args="pub /calibrated std_msgs/Bool true" />
  <!--  
  <node pkg="vswarm_sim" type="swarmsim.py" name="swarmsim" />
  -->

<group ns="0">
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam_ctrl.urdf.xacro ns:=0" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" args="-urdf -model robot0 -param robot_nocam_description  -x 2.0 -y 0.0"/>
</group>

<group ns="1">
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam_ctrl.urdf.xacro ns:=1" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" args="-urdf -model robot1 -param robot_nocam_description -x 0.0 -y 0.0"/>
</group>

<group ns="2">
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam_ctrl.urdf.xacro ns:=2" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" args="-urdf -model robot2 -param robot_nocam_description  -x 1.0 -y 0.0"/>
</group>

<group ns="3">
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam_ctrl.urdf.xacro ns:=3" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" args="-urdf -model robot3 -param robot_nocam_description  -x 0.0 -y 1.0"/>
</group>

<group ns="4">
  <param name="robot_nocam_description" command="$(find xacro)/xacro $(find vswarm_sim)/urdf/car_nocam_ctrl.urdf.xacro ns:=4" />
  <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" args="-urdf -model robot4 -param robot_nocam_description  -x 1.0 -y 1.0"/>
</group>
</launch>
