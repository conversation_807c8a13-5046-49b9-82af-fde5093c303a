#!/bin/bash

NAME=$(hostname)

xhost +

docker run -it -d \
    --runtime=nvidia \
    --restart=always \
    --privileged \
    --gpus all \
    --hostname $NAME \
    --add-host $NAME:127.0.0.1 \
    --name swarm \
    -v $(pwd):/home/<USER>
    -v /dev:/dev \
    -v /tmp/.X11-unix:/tmp/.X11-unix \
    -v $(pwd)/config/supervisor.conf:/etc/supervisor/conf.d/swarm.conf \
    -v /home/<USER>/lk_ws/gcn_nri:/home/<USER>/lk_ws/gcn_nri \
    --workdir /home/<USER>
    --network host \
    --env DISPLAY=$DISPLAY \
    --env REMOTE_SERVER=********* \
    --env BOOT_FILE=/home/<USER>/utils/scripts/real_boot.sh \
    *********:6000/huabench/swarm-car \
    bash -c "supervisord && tail -f /dev/null"
