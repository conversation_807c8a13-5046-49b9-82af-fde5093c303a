#!/bin/bash

# 简单脚本：停止Docker容器中的相机占用
# 用于本地开发时释放相机设备

echo "停止Docker容器中的相机节点..."

# 检查容器是否运行
if ! docker ps | grep -q "swarm"; then
    echo "错误: swarm容器未运行"
    exit 1
fi

# 停止所有相机相关进程
docker exec swarm pkill -f "usb_cam" 2>/dev/null || true
docker exec swarm pkill -f "roslaunch.*vswarm-quad" 2>/dev/null || true

# 强制停止（如果普通停止无效）
sleep 2
docker exec swarm pkill -9 -f "usb_cam" 2>/dev/null || true

# 等待进程完全停止
sleep 3

# 验证结果
camera_nodes=$(docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rosnode list 2>/dev/null | grep cam" 2>/dev/null || echo "")

if [ -z "$camera_nodes" ]; then
    echo "✓ 相机节点已停止，设备已释放"
    echo "现在可以进行本地开发了"
else
    echo "警告: 部分相机节点可能仍在运行:"
    echo "$camera_nodes"
fi

echo ""
echo "测试所有相机的本地访问:"
python3 -c "
import cv2
import sys

# 测试实际的相机设备 (video0, video2, video4, video6)
camera_devices = [0, 2, 4, 6]  # 对应 /dev/video0, /dev/video2, /dev/video4, /dev/video6
success_count = 0

for i, device_id in enumerate(camera_devices):
    try:
        # 尝试打开相机设备
        cap = cv2.VideoCapture(device_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f'✓ 相机{i} (/dev/video{device_id}) 可以访问 ({frame.shape[1]}x{frame.shape[0]})')
                success_count += 1
            else:
                print(f'✗ 相机{i} (/dev/video{device_id}) 无法读取数据')
        else:
            print(f'✗ 相机{i} (/dev/video{device_id}) 无法打开')
        cap.release()
    except Exception as e:
        print(f'✗ 相机{i} (/dev/video{device_id}) 错误: {e}')

print(f'\\n总计: {success_count}/4 个相机可用于本地开发')

if success_count == 4:
    print('🎉 所有相机都已释放，可以开始本地开发！')
elif success_count > 0:
    print('⚠️  部分相机可用，可能还有进程占用某些设备')
else:
    print('❌ 所有相机都无法访问，请检查设备连接或容器状态')

print('\\n💡 本地开发示例:')
print('   import cv2')
for i, device_id in enumerate(camera_devices):
    print(f'   cap{i} = cv2.VideoCapture({device_id})  # 相机{i}')
"
