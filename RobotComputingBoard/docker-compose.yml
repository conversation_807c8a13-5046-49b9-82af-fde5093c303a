version: '3'

services:
  build:
    image: huabench/swarm-car
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/home/<USER>
    working_dir: /home/<USER>/real_ws
    command: bash build.sh
    
  # run:
  #   image: huabench/swarm-car
  #   depends_on:
  #     - build
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   privileged: true
  #   hostname: VSWARM3
  #   extra_hosts:
  #     - "VSWARM3:127.0.0.1"
  #   volumes:
  #     - .:/home/<USER>
  #     - /dev:/dev
  #     - /tmp/.X11-unix:/tmp/.X11-unix
  #     - ./config/supervisor.conf:/etc/supervisor/conf.d/swarm.conf
  #   working_dir: /home/<USER>
  #   network_mode: host
  #   stdin_open: true
  #   tty: true
  #   environment:
  #     - DISPLAY=$DISPLAY
  #     - REMOTE_SERVER=*********
  #   command: bash -c "supervisord && tail -f /dev/null"