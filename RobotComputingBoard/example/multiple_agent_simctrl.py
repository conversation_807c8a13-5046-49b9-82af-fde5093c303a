import time
import threading

import numpy as np
from scipy.spatial.transform import Rotation as R

import rospy
from geometry_msgs.msg import Twist
from nav_msgs.msg import Odometry
from gazebo_msgs.msg import ModelStates

# 取消科学计数法
np.set_printoptions(edgeitems=3, suppress=True, infstr='inf')
agent_threads = []

class control_node():
    def __init__(self,id):
        self.id = id
        self.topic_name = '/' + str(id) 
        self.position_x = 0
        self.position_y = 0 
        self.targetz = 0
        self.zvel = 0
        self.tick = 0
        self.euler = np.array([0,0,0])
        self.vel_pub = rospy.Publisher(self.topic_name + '/robot/velcmd', Twist, queue_size=10)
        #获取自身的位置
        rospy.Subscriber(self.topic_name + '/robot/odom',Odometry, self.odom_callback)
        #获取其他模型的位置
        rospy.Subscriber('/gazebo/model_states',ModelStates,self.modelstates_callback) 
        
        print("creat agent robot" + str(id) )
    def odom_callback(self, msg):
        self.position_x = msg.pose.pose.position.x
        self.position_y = msg.pose.pose.position.y
        
        quaternion = np.array([msg.pose.pose.orientation.x, msg.pose.pose.orientation.y, msg.pose.pose.orientation.z, msg.pose.pose.orientation.w])
        rot_mat = R.from_quat(quaternion).as_matrix()
        # 解出欧拉角（RPY顺序）
        self.euler = np.array(R.from_matrix(rot_mat).as_euler('xyz', degrees=False))
        # print(self.id, self.euler)
        # print("odmo",quaternion)
        
    def modelstates_callback(self, msg):
        for i,x in enumerate(msg.name):
            if x == 'robot0':
                pose=msg.pose[i]
                quat=pose.orientation
                pos=pose.position
                pos_x = pos.x
                pos_y = pos.y
                pos_z = pos.z
                # print("modelstates",quat)
                
    def vel_publish(self, x, y):
        vel3 = Twist()
        vel3.linear.x = x
        vel3.linear.y = y
        vel3.angular.z = self.zvel 
        self.vel_pub.publish(vel3)
        # print(self.id, vel3)
    def yaw_pid_control(self, target):
        kp = 5
        kd = 0.5
        dt = 0.01
        error = target - self.euler[2]
        if error > 3.14:
            error = error -6.28
        if error < -3.14:
            error = error + 6.28
        # print(error)
        last_error = error
        self.i =error
        self.zvel = error * kp + (error -last_error) / dt  * kd 
    
    def control_test(self):
        x = 0 
        y = 0
        self.tick = self.tick + 1
        if (self.tick>0 and self.tick<100) :
            x = 0.0
            y = 0.0
        elif (self.tick>=100 and self.tick<300) :
            x = 0.2
        elif (self.tick>=300 and self.tick<500) :
            y=  0.2
        else :
            self.targetz = self.targetz + 1.57
            if self.targetz > 3.14:
                self.targetz = -1.57
            self.tick = 0
        self.vel_publish(x, y)
        # print(self.targetz)
    def run(self):
        while True:
            self.yaw_pid_control(self.targetz)
            self.control_test()
            time.sleep(0.01)
class ros_node():
    def __init__(self):
        pass
    
if __name__ == "__main__":
    
   
    rospy.init_node("robotCtrl")
    ros_hz = 100
    rate = rospy.Rate(ros_hz)
    num_agents = 5

    agents = [control_node(i) for i in range(num_agents)]
    # 启动所有agent节点 
    # 使用线程的方法做伪分布式 一个线程就算一个分布式的节点 一个节点抽象成了control_node类
    for agent in agents:
        thread = threading.Thread(target=agent.run)
        agent_threads.append(thread)
        thread.start()
        
    while not rospy.is_shutdown():

        rate.sleep()