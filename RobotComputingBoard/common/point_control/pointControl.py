#!/usr/bin/python3.8
import rospy
from idPD import idPD
from nav_msgs.msg import Odometry
import socket
import tf
import numpy as np
from geometry_msgs.msg import PoseStamped, Twist, TransformStamped


rospy.init_node('point_ctrl',anonymous=True)
node_name = rospy.get_name()


class PointControl:
    def __init__(self, odom_type, odom_src):
        self.odom_type = odom_type
        self.odom_src = odom_src
        self.x_goal = 1.5
        self.y_goal = -2
        
        self.alpha = 1.0
        
        self.x_ctrl=idPD(P=4.5, D=2.3, scal=0.15*self.alpha, alpha=0.1, thres=0.8)
        self.y_ctrl=idPD(P=4.5, D=2.3, scal=0.15*self.alpha, alpha=0.1, thres=0.8)
        self.z_ctrl=idPD(P=30, D=2.3, scal=0.15*self.alpha, alpha=0.1, thres=10)
        self.set_topic()
        
        self.vel_pub = rospy.Publisher('robot/velcmd', Twist, queue_size=10)
        
    def set_topic(self):
        if odom_type == 'vicon':
            rospy.Subscriber(self.odom_src, TransformStamped, self.vicon_callback)
        else:
            rospy.Subscriber("/robot/odom",Odometry,self.pos_callback)
        
        rospy.Subscriber("/point_control/goal", PoseStamped, self.goal_callback)
        

    def pos_callback(self, msg):
        x_real = msg.pose.pose.position.x
        y_real = msg.pose.pose.position.y
        
        (roll, pitch, yaw)= tf.transformations.euler_from_quaternion([msg.pose.pose.orientation.x, 
                                                  msg.pose.pose.orientation.y,
                                                  msg.pose.pose.orientation.z,
                                                  msg.pose.pose.orientation.w])
        
        diff_x = self.x_goal - x_real
        diff_y = self.y_goal - y_real
        sp_x = self.x_ctrl.ctrl(diff_x)
        sp_y = self.y_ctrl.ctrl(diff_y)
        sp_z = -self.z_ctrl.ctrl(yaw)
        temp = np.array([sp_x,sp_y])
        motion_cmd = Twist()

        if np.linalg.norm(temp) > 0.15:
            temp = temp / np.linalg.norm(temp)  * 0.15
        motion_cmd.linear.x = -temp[0]
        motion_cmd.linear.y = -temp[1]
        motion_cmd.angular.z = sp_z
        self.vel_pub.publish(motion_cmd)
    
    def vicon_callback(self, msg):
        
        x_real = msg.transform.translation.x
        y_real = msg.transform.translation.y
        
        (roll, pitch, yaw)= tf.transformations.euler_from_quaternion([msg.transform.rotation.x, 
                                                    msg.transform.rotation.y,
                                                    msg.transform.rotation.z,
                                                    msg.transform.rotation.w])
        
        diff_x = self.x_goal - x_real
        diff_y = self.y_goal - y_real
        diff_z = yaw
        
        
        sp_x = -self.x_ctrl.ctrl(diff_x)
        sp_y = -self.y_ctrl.ctrl(diff_y)
        sp_z = -self.z_ctrl.ctrl(diff_z)

        temp = np.array([sp_x,sp_y])
        motion_cmd = Twist()

        if np.linalg.norm(temp) > 0.95:
            temp = temp / np.linalg.norm(temp)  * 0.95
        motion_cmd.linear.x = -temp[0]
        motion_cmd.linear.y = -temp[1]
        motion_cmd.angular.z = sp_z
        self.vel_pub.publish(motion_cmd)
        
    def goal_callback(self, msg):
        self.x_goal = msg.pose.position.x
        self.y_goal = msg.pose.position.y
    
    
if __name__ == "__main__":
    # rospy.Subscriber("/robot/odom",Odometry,pos_callback)
    odom_type = "vicon"#rospy.get_param(node_name + "/odom_type", f"vicon")
    odom_src = rospy.get_param(node_name + "/odom_src", f"/vicon/{socket.gethostname()}/{socket.gethostname()}")
    
    pCtr = PointControl(odom_type, odom_src)
    
    
    while not rospy.is_shutdown():
        pass
    rospy.spin()
