#获取当前文件目录
export DISPLAY=:0
cd $(dirname $BASH_SOURCE)
{
    #启动定位
    gnome-terminal -x bash -c "source /opt/ros/noetic/setup.bash;source ../../real_ws/vicon_ws/devel/setup.bash;python3 localization_boot.py;exec bash"
    sleep 5
}
{
    #启动摄像头节点并修复连接问题
    gnome-terminal -x bash -c "source /opt/ros/noetic/setup.bash;python3 cam_boot.py;exec bash"
    sleep 15
}
{
    #启动控制板
    gnome-terminal -x bash -c "source /opt/ros/noetic/setup.bash;python3 board_com_boot.py;exec bash"
    sleep 5
}
{
    #定义MQTT服务器地址 
    gnome-terminal -x bash -c "source /opt/ros/noetic/setup.bash;export REMOTE_SERVER=10.0.2.66;python3 ../../common/robot_server/control_server.py;exec bash"
}
cd ../..
