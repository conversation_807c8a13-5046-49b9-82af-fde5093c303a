 #获取当前文件目录
exec >/dev/null

(
    #启动定位
    source ../../real_ws/vicon_ws/devel/setup.bash
    python3 localization_boot.py
    # sleep 5
) &
(
    #启动摄像头节点并修复连接问题
    sleep 5
    python3 cam_boot.py >/dev/null 2>&1
) &
(
    #启动控制板
    sleep 5
    python3 board_com_boot.py >/dev/null 2>&1
) &
(
    #定义MQTT服务器地址
    sleep 5
    export REMOTE_SERVER=10.0.2.66
    python3 ../../common/robot_server/control_server.py >/dev/null 2>&1
)
