import cv2, glob
import numpy as np
import os
import json

#print("rename all fils")
cam_name_list = ['front','left','rear','right']
cam_id = 3
cam_name = cam_name_list[cam_id]
files = glob.glob(f'./cache/{cam_name}/*jpg')
print(files)
i = 0
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 50, 0.0001)

w = 11
h = 8

objp = np.zeros((w*h,3), np.float32)
objp[:,:2] = np.mgrid[0:w,0:h].T.reshape(-1,2)
objp = objp

objpoints = []
imgpoints = []

for fname in files:
    print(fname)
    img = cv2.imread(fname)
  #  img = cv2.resize(img,(img.shape[1], img.shape[0]//6), interpolation=cv2.INTER_CUBIC)
    h1, w1 = img.shape[0], img.shape[1]
    gray = cv2.cvtColor(img,cv2.COLOR_BGR2GRAY)
    u, v = img.shape[:2]
    ret, corners = cv2.findChessboardCorners(gray, (w,h),None)
    if ret == True:
        cv2.cornerSubPix(gray,corners,(3,3),(-1,-1),criteria)
        objpoints.append(objp)
        imgpoints.append(corners)
        cv2.drawChessboardCorners(img, (w,h), corners, ret)
        cv2.namedWindow('findCorners')
        cv2.imshow('findCorners',img)
        cv2.waitKey()
cv2.destroyAllWindows()
ret, mtx, dist, rvecs, tvecs = \
    cv2.calibrateCamera(objpoints, imgpoints, gray.shape[::-1], None, None)

print("ret:",ret  )
print("mtx:\n",mtx)
print("dist\n",dist   )


data = {
    "cam_id": cam_id,
    "ret": ret,
    "mtx": mtx.tolist(),  
    "dist": dist.tolist()  #
}
with open(f'{cam_id:03d}.json', 'w') as json_file:
    json.dump(data, json_file, indent=4)