# 集群小车自动部署指南

本文档提供了一套步骤，用于将代码自动部署到所有在集群中的小车。

## 步骤 1: 添加小车信息到 Ansible 配置

您需要将每辆小车的信息添加至 `config/hosts` 文件中。格式如下所示：

```ini
[SWARM]
************* ansible_user=nvidia ansible_password=123
# new_IP ansible_user=username ansible_password=password
```

请确保替换 `new_IP`、`username` 和 `password` 为实际的 IP 地址和相应的小车凭据。

## 步骤 2: 使用 Docker Compose 启动容器

如果使用science华奔的路由器，不需要这一步？

首先需要配置git和nas的用户名和密码，

在`deploy/roles/setup_environment/vars`路径下，创建`git_credentials.yml`

```yml
git_username: your_gitlab_username
git_password: your_gitlab_password
```

在`deploy/roles/finish/vars`路径下，创建`nas_credentials.yml`

```yml
nas_username: yout_nas_username
nas_password: your_nas_username
```

在docker目录下执行以下命令以启动 Docker 容器，并自动执行 Ansible 指令部署代码：

```sh
docker-compose up
```

这将会启动定义在 `main.yml` 文件中的脚本。

## 关于`main.yml` 与 `hosts`的配置

```
[run_base]
************** ansible_user=nvidia ansible_password=123
# ************* ansible_user=nvidia ansible_password=123
# ************** ansible_user=nvidia ansible_password=123
# ************** ansible_user=nvidia ansible_password=123

[new]
************** ansible_user=nvidia ansible_password=123
```

`hosts`内，将全新没有部署过的小车放进组`new`内（运行过一次就要将小车从这个组里移出来），
将部署过小车但是还没有启动基础程序的，放进run_base里面。

`main.yml`说明

```yml
  vars:
    git_repo_path: /home/<USER>/docker/RobotComputingBoard
    stage: 3
```
在部署阶段，将stage设置为-1；需要编译代码并且运行基础程序，stage设置为0。
stage=1时，是实验前准备阶段，stage=2则会运行用户自定义的脚本（包含运行rosbag），
stage=3时，会停止stage 2的程序并上传bag文件到NAS。

boot_file  sim_boot.sh是gazebo real_bool.sh 实车.

使用gazebo，如果一开始没有接显示器，   DISPLAY环境变量是空的，可能存在问题

## 基础镜像拉取



## 完成部署

一旦 Ansible Playbook 运行完毕，代码将被更新到集群中所有指定的小车上。确保所有的小车均已正确连接到网络并且可通过 SSH 访问。

## 注意

在执行 Ansible Playbook 之前，请仔细检查 `config/hosts` 文件中的小车凭据，确保它们是正确和最新的，以避免运行过程中出现权限问题。

如有任何疑问，请联系维护人员 <EMAIL>
