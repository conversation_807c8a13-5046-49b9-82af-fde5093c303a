<launch>
  <node name="cam0" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam0" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam0" />
    <param name="io_method" value="mmap"/>
  </node>

 <node name="cam1" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam1" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam1" />
    <param name="io_method" value="mmap"/>
  </node>

 <node name="cam2" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam2" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam2" />
    <param name="io_method" value="mmap"/>
  </node>

 <node name="cam3" pkg="usb_cam" type="usb_cam_node" >
    <param name="video_device" value="/dev/cam3" />
    <param name="image_width" value="640" />
    <param name="image_height" value="360" />
    <param name="pixel_format" value="mjpeg" />
    <param name="camera_frame_id" value="usb_cam3" />
    <param name="io_method" value="mmap"/>
  </node>
</launch>
