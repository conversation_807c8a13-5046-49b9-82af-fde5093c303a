FROM ubuntu:20.04

# 使用清华大学的镜像源
RUN sed -i 's|http://archive.ubuntu.com/ubuntu|http://mirrors.tuna.tsinghua.edu.cn/ubuntu|g' /etc/apt/sources.list && \
    sed -i 's|http://security.ubuntu.com/ubuntu|http://mirrors.tuna.tsinghua.edu.cn/ubuntu|g' /etc/apt/sources.list

RUN apt-get update && apt-get install -y \
    openssh-client \
    iputils-ping \
    ansible \
    python3-pip \
    sshpass

RUN pip install ansible

RUN mkdir -p /etc/ansible && printf '[defaults]\nhost_key_checking = False\n' > /etc/ansible/ansible.cfg

# 这样可以让运行的容器停留在前台，方便查看输出
CMD ["/bin/bash"]