- name: Configure <PERSON><PERSON> to access HTTP private registry
  hosts: all
  become: yes
  become_method: sudo
  become_user: root
  vars:
    ansible_become_pass: 123

  tasks:
    - name: load var from file
      slurp:
        src: /etc/docker/daemon.json
      register: docker_conf

    - debug:
        msg: "{{ docker_conf.content|b64decode|from_json }}"

    - name: Check if insecure-registries already exists
      set_fact:
        skip_modify_conf: "{{ 'insecure-registries' in docker_conf.content|b64decode|from_json }}"

    - name: append more key/values
      when: not skip_modify_conf
      set_fact:
        docker_conf: "{{ docker_conf.content|b64decode|from_json | default([]) | combine({ 'insecure-registries':['*********:6000'] }) }}"

    - debug:
        var: docker_conf

    - name: write var to file
      when: not skip_modify_conf
      copy: 
        content: "{{ docker_conf | to_nice_json }}" 
        dest: /etc/docker/daemon.json

    - name: <PERSON><PERSON> Docker
      when: not skip_modify_conf
      systemd:
        name: docker
        state: restarted
        enabled: yes

    - name: Docker pull
      shell: docker pull *********:6000/huabench/swarm-car &&
             docker tag *********:6000/huabench/swarm-car huabench/swarm-car

    - name: Add to docker group
      command: usermod -aG docker {{ ansible_user }}
