- name: Deploy code
  hosts: all
  become: yes
  become_method: sudo
  become_user: root
  vars:
    ansible_become_pass: 123
    git_repo_path: /home/<USER>/docker/RobotComputingBoard

  tasks:
    - name: Ensure git is installed
      apt:
        name: git
        state: present
      become: yes
      ignore_errors: true

    - name: Check if project directory exists
      stat:
        path: "{{ git_repo_path }}"
      register: project_dir

    - name: Include git credentials
      include_vars: git_credentials.yml

    - name: Clone git repository if project directory does not exist
      git:
        repo: "http://{{ git_username }}:{{ git_password }}@*********:8000/VSWARM/RobotComputingBoard.git"
        dest: "{{ git_repo_path }}"
      when: project_dir.stat.exists == False

    - name: Set ownership of the project directory
      file:
        path: "{{ git_repo_path }}"
        owner: "nvidia"
        group: "nvidia"
        recurse: yes
      when: project_dir.stat.exists == False

    - name: switch to develop branch
      command: git checkout develop
      args:
        chdir: "{{ git_repo_path }}"

    - name: get latest code
      command: git pull origin main
      args:
        chdir: "{{ git_repo_path }}"

    - name: Copy usb.rules to /etc/udev/rules.d
      copy:
        src: usb.rules
        dest: /etc/udev/rules.d/usb.rules

    - name: Compile Code
      shell: bash build.sh
      args:
        chdir: "{{ git_repo_path }}"

    - name: Create a directory if it does not exist
      file:
        path: "{{ git_repo_path }}/../user_ws"
        state: directory
        mode: '0755'
        owner: nvidia
        group: nvidia

    - name: Stop and remove Docker container
      command: docker rm -f swarm user_swarm

    - name: Run docker container
      shell: bash docker_run.sh
      args:
        chdir: "{{ git_repo_path }}"

