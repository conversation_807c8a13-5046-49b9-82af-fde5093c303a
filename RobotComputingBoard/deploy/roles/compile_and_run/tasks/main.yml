---
# tasks file for compile_and_run

- name: Deleted project
  command: git reset --hard HEAD
  args:
    chdir: "{{ git_repo_path }}" 
  when: update_code | default(false) and force | default(false)

- name: Update and compile
  include_tasks: update_code.yml
  when: update_code | default(false)

- name: Stop and remove Docker containers
  command: docker rm -f {{ item }}
  loop:
    - swarm
    - user-swarm

- name: Run docker container
  shell: bash docker_run.sh
  args:
    chdir: "{{ git_repo_path }}"
  environment:
    BOOT_FILE: "{{ boot_file }}"