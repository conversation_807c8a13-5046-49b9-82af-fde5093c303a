---
# tasks file for prepare

- name: Get ROS topic hz
  shell: docker exec -it swarm bash -c "source /opt/ros/noetic/setup.bash && timeout 4 rostopic hz {{ item }}"
  register: topic_hz_results
  loop: "{{ topics }}"
  loop_control:
    label: "{{ item }}"
  ignore_errors: yes
  failed_when: "'average rate' not in topic_hz_results.stdout"

- name: Display results
  debug:
    msg: "Topic {{ item.item }}, frequency: {{ item.stdout | regex_search('average rate: (\\d+\\.\\d+)', '\\1') }}"
  loop: "{{ topic_hz_results.results }}"
  loop_control:
    loop_var: item
