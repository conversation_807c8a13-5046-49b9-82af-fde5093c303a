---
# tasks file for finish

- name: Stop rosbag recording
  command: docker container stop ros_bag

# - name: Install sshpass on Debian/Ubuntu
#   apt:
#     name: sshpass
#     state: present

# - name: Include NAS info
#   include_vars: nas_credentials.yml
    
# - name: Copy file to other servers
#   command: sshpass -p {{ nas_password }} scp /home/<USER>/docker/RobotComputingBoard/build.sh {{ nas_username }}@10.0.2.66:/var/services/homes/huabench
#   become: true
#   become_user: nvidia