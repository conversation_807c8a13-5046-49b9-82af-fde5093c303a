---
# tasks file for setup_environment

- name: load var from file
  slurp:
    src: "{{ docker_config_file }}"
  register: docker_conf

- debug:
    msg: "{{ docker_conf.content|b64decode|from_json }}"

- name: Check if insecure-registries already exists
  set_fact:
    skip_modify_conf: "{{ 'insecure-registries' in docker_conf.content|b64decode|from_json }}"

- name: append more key/values
  when: not skip_modify_conf
  set_fact:
    docker_conf: "{{ docker_conf.content|b64decode|from_json | default([]) | combine({ 'insecure-registries':[private_registry_address] }) }}"

- debug:
    var: docker_conf

- name: write var to file
  when: not skip_modify_conf
  copy: 
    content: "{{ docker_conf | to_nice_json }}" 
    dest: "{{ docker_config_file }}"
  notify:
    - Restart Docker
    - Docker pull

- name: Add to docker group
  command: usermod -aG docker {{ ansible_user }}

- name: Copy usb.rules to /etc/udev/rules.d
  copy:
    src: usb.rules
    dest: /etc/udev/rules.d/usb.rules

- name: Includ git info
  include_vars: git_credentials.yml

- name: Include setup_project task
  include_tasks: setup_project.yml

