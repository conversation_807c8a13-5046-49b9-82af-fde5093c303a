# - name: Ensure git is installed
#   apt:
#     name: git
#     state: present
#   become: yes
#   ignore_errors: true

- name: Check if project directory exists
  stat:
    path: "{{ git_repo_path }}"
  register: project_dir

- name: Clone git repository if project directory does not exist
  git:
    repo: "http://{{ git_username }}:{{ git_password }}@10.0.2.66:8000/VSWARM/RobotComputingBoard.git"
    dest: "{{ git_repo_path }}"
  when: project_dir.stat.exists == False
  become_user: nvidia


- name: switch to develop branch
  command: git checkout develop
  args:
    chdir: "{{ git_repo_path }}"
  become_user: nvidia

- name: Create a directory if it does not exist
  file:
    path: "{{ git_repo_path }}/../user_ws"
    state: directory