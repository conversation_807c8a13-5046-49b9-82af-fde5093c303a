---
# tasks file for user_run

- name: Ensure directory exists
  file:
    path: /home/<USER>/rosbag
    state: directory
    mode: 0755
    owner: nvidia
    group: nvidia

- name: Start rosbag recording asynchronously
  command: >
    docker run -it -d --rm --network=host --name ros_bag \
    -v /home/<USER>/rosbag:/data huabench/swarm-car bash -c \
    "source /opt/ros/noetic/setup.bash \
    && cd /data && rosbag record /robot/imu"
  args:
    chdir: /home/<USER>
  async: 3000  # 设置任务的超时时间（秒），这里设置为 300 秒（5分钟）
  poll: 0  # 立即返回，不等待任务完成
  become: true
  become_user: nvidia  # 替换为你的 ROS 用户
  register: rosbag_task

- name: Start User tasks
  command: >
    nvidia-docker run -it -d
        --hostname $NAME
        --add-host $NAME:127.0.0.1
        --name user-swarm
        -v /home/<USER>/docker/user_ws:/home/<USER>
        -v /home/<USER>/lk_ws/gcn_nri:/home/<USER>/lk_ws/gcn_nri 
        --workdir /home/<USER>
        --network host
        --env REMOTE_SERVER=*********
        huabench/swarm-car
        bash -c "{{ user_command }} && tail -f /dev/null"
  args:
    chdir: /home/<USER>/docker/user_ws
  async: 3000  # 设置任务的超时时间（秒），这里设置为 300 秒（5分钟）
  poll: 0  # 立即返回，不等待任务完成
  become: true
  become_user: nvidia  # 替换为你的 ROS 用户
  register: user_task
