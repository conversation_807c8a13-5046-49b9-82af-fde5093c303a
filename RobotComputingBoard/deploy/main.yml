- name: test
  hosts: all
  gather_facts: no
  become: yes
  become_method: sudo
  become_user: root
  vars:
    ansible_become_pass: 123
    git_repo_path: /home/<USER>/docker/RobotComputingBoard
    boot_file: "real_boot.sh"
    ansible_verbosity: 1  # 设置为 0 表示最小输出
    user_command: "touch test.log"
    stage: 3
  roles:
    - role: setup_environment
      when:  "'new' in group_names"

    - role: compile_and_run
      vars:
        update_code: false
        force: false
      when: stage == 0 and "'run_base' in group_names"

    - role: prepare
      when: stage == 1
    - role: user_run
      when: stage == 2
    - role: finish
      when: stage == 3
