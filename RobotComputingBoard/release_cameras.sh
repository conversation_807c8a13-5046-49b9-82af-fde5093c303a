#!/bin/bash

echo "🔄 停止Docker容器中的相机节点..."

# 检查容器是否运行
if ! docker ps | grep -q "swarm"; then
    echo "❌ 错误: swarm容器未运行"
    exit 1
fi

# 停止相机进程
echo "   停止usb_cam进程..."
docker exec swarm pkill -9 -f "usb_cam" 2>/dev/null || true

# 等待进程停止
sleep 2

# 验证相机节点状态
echo "📋 检查相机节点状态..."
camera_nodes=$(docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rosnode list 2>/dev/null | grep cam" 2>/dev/null || echo "")

if [ -z "$camera_nodes" ]; then
    echo "✅ 容器相机节点已停止"
else
    echo "⚠️  仍有相机节点运行:"
    echo "$camera_nodes"
fi

echo ""
echo "🧪 测试本地相机访问..."

# 测试所有4个相机 (video0, video2, video4, video6)
python3 << 'EOF'
import cv2
import sys

camera_devices = [0, 2, 4, 6]  # 对应实际的相机设备
success_count = 0

for i, device_id in enumerate(camera_devices):
    try:
        cap = cv2.VideoCapture(device_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f'✅ 相机{i} (/dev/video{device_id}) 可访问 - {frame.shape[1]}x{frame.shape[0]}')
                success_count += 1
            else:
                print(f'❌ 相机{i} (/dev/video{device_id}) 无法读取数据')
        else:
            print(f'❌ 相机{i} (/dev/video{device_id}) 无法打开')
        cap.release()
    except Exception as e:
        print(f'❌ 相机{i} (/dev/video{device_id}) 错误: {str(e)[:50]}...')

print(f'\n📊 结果: {success_count}/4 个相机可用于本地开发')

if success_count == 4:
    print('🎉 所有相机都已释放，可以开始本地开发！')
    print('\n💡 本地开发示例:')
    print('   import cv2')
    for i, device_id in enumerate(camera_devices):
        print(f'   cap{i} = cv2.VideoCapture({device_id})  # 相机{i}')
elif success_count > 0:
    print('⚠️  部分相机可用，建议重新运行脚本')
else:
    print('❌ 无相机可用，请检查设备连接')
EOF

echo ""
echo "✨ 相机释放完成！"
