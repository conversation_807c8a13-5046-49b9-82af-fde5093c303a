# README

## 环境依赖

- ROS1版本`noetic`

  - **串口包**：`sudo apt-get install ros-noetic-serial`
  - **摄像头包**：`sudo apt-get install ros-noetic-usb-cam`
  
- 需要设置USB端口绑定与更改`usb_cam launch`文件来适配底板与镜头操作方法如下

  - 移动``usb.rules``到`/etc/udev/rules.d/`并修改如下内容

    ```bash
    ACTION=="add", KERNEL=="video[0,2,4,6,8]*", KERNELS=="1-2.4.1:1.0",SUBSYSTEMS=="usb",MODE:="0777",SYMLINK+="cam0"
    
    ACTION=="add", KERNEL=="video[0,2,4,6,8]*", KERNELS=="1-2.4.2:1.0",SUBSYSTEMS=="usb",MODE:="0777",SYMLINK+="cam1"
    
    ACTION=="add", KERNEL=="video[0,2,4,6,8]*", KERNELS=="1-2.4.3:1.0",SUBSYSTEMS=="usb",MODE:="0777",SYMLINK+="cam2"
    
    ACTION=="add", KERNEL=="video[0,2,4,6,8]*", KERNELS=="1-2.4.4:1.0",SUBSYSTEMS=="usb",MODE:="0777",SYMLINK+="cam3"
    ```

    将如上的`KERNELS=="1-2.4.1:1.0"`改为自己主板所连接端口，端口查询方法如下

    ```bash
    sudo apt install v4l-utils
    udevadm info /dev/video0
    ```

  - 移动

## 目录说明

RobotComputingBoard（板载计算机）

- **docs**
- **bag**：记录数据
- **utils**：工具脚本
  - scripts
  - inference_server
- **sim_ws**：包含仿真环境实验完整的软件包
  - vswarm_sim_ws
- **real_ws**：包含真实环境实验完整的软件包
  - vicon_ws
  - board_com_ws
  - ...
- **users**：用户程序
- **common**
  - robot_server

## **文档链接**

[集群机器人协议文档](./docs/real/集群机器人控制端通信协议.md )：板载计算机通过USB与控制板通信的通信协议及相关。

[集群机器人板载接口](./docs/real/集群机器人板载接口.md )：板载计算机ROS端的操作接口，包括控制指令与传感信息。

[分布式仿真启动方法](./docs/sim/README.md )：启动分布式仿真的方法。

[代码自动部署](./docs/自动部署.md): 用于将代码自动部署到所有在集群中的小车
