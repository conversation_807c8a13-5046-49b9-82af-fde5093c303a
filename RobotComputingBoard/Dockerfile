FROM ros:noetic-robot-focal

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y \
    ros-noetic-gazebo-ros \
    ros-noetic-gazebo-ros-control \
    ros-noetic-gazebo-plugins \
    ros-noetic-cv-bridge \
    ros-noetic-serial \
    ros-noetic-usb-cam \
    ros-noetic-rqt \
    ros-noetic-rqt-common-plugins \
    ros-noetic-compressed-image-transport \
    vim \
    python3-pip \
    iputils-ping \
    supervisor

COPY requirements.txt requirements.txt
COPY deploy/vswarm-quad-360p.launch /opt/ros/noetic/share/usb_cam/launch

RUN pip3 install -r requirements.txt && \
    echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc

CMD ["/bin/bash"]