# Docker环境修复完整记录

## 📋 问题概述

**日期**: 2025-08-19  
**系统**: NVIDIA Jetson (VSWARM7)  
**主要问题**: Docker容器无法启动，磁盘空间不足，相机节点无法工作

---

## 🚨 初始问题状态

### 1. 磁盘空间严重不足
```bash
# 初始状态
/dev/nvme0n1p1   99G   93G   2.1G   98% /
```
- **问题**: 磁盘使用率98%，只剩2.1GB可用空间
- **影响**: Docker无法创建容器，系统运行缓慢

### 2. Docker容器启动失败
```bash
# 错误信息
failed to create task for container: failed to create shim task: 
OCI runtime create failed: runc create failed: unable to start container process: 
error during container init: error mounting "/lib/firmware/tegra19x" to rootfs: 
lstat /var/lib/docker/overlay2/.../merged/usr/lib: input/output error
```
- **问题**: Docker overlay2存储层损坏，I/O错误
- **影响**: 容器无法启动

### 3. ROS网络配置问题
```bash
# roscore启动失败
RLException: Unable to contact my own server at [http://VSWARM7:32777/].
This usually means that the network is not configured properly.
```
- **问题**: 主机名VSWARM7无法解析
- **影响**: ROS系统无法启动

### 4. 相机节点无数据
```bash
# 相机话题无输出
rostopic echo /cam0/image_raw  # 无输出
```
- **问题**: 相机设备映射错误
- **影响**: 4个相机都无法工作

---

## 🔧 解决方案详细步骤

### 第一阶段：磁盘空间清理

#### 1.1 删除大文件目录
```bash
# 删除jetson用户目录 (释放17GB)
sudo pkill -u jetson
sudo systemctl stop nano_jupyter.service
sudo systemctl disable nano_jupyter.service
sudo rm -rf /home/<USER>

# 删除Ollama AI模型 (释放33GB)
sudo systemctl stop ollama
sudo systemctl disable ollama
sudo rm -rf /usr/share/ollama
```

#### 1.2 系统清理
```bash
# 清理包缓存
sudo apt clean
sudo apt autoremove --purge

# 清理系统日志
sudo journalctl --vacuum-time=1d
```

**结果**: 磁盘使用率从98%降到31%，释放50GB空间

### 第二阶段：Docker存储修复

#### 2.1 停止Docker服务
```bash
sudo systemctl stop docker
sudo systemctl stop docker.socket
```

#### 2.2 删除损坏的存储
```bash
# 备份配置
sudo cp -r /etc/docker /etc/docker.backup

# 删除损坏的存储层
sudo rm -rf /var/lib/docker
```

#### 2.3 重启Docker服务
```bash
sudo systemctl start docker
```

**结果**: Docker存储重建，释放额外14GB空间

### 第三阶段：Docker配置修复

#### 3.1 配置NVIDIA运行时
```bash
# 创建正确的daemon.json
sudo tee /etc/docker/daemon.json << 'EOF'
{
    "insecure-registries": [
        "*********:6000"
    ],
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    }
}
EOF

sudo systemctl restart docker
```

#### 3.2 修复docker_run.sh脚本
```bash
# 修改脚本使用正确的镜像和运行时
--env BOOT_FILE=/home/<USER>/utils/scripts/real_boot.sh
--runtime=nvidia
*********:6000/huabench/swarm-car
```

**结果**: Docker容器成功启动，MQTT自动连接

### 第四阶段：ROS网络修复

#### 4.1 修复主机名解析
```bash
# 添加主机名映射
echo "127.0.0.1       VSWARM7" | sudo tee -a /etc/hosts
```

**结果**: ROS master正常启动

### 第五阶段：相机系统修复

#### 5.1 分析相机设备
```bash
# 发现设备模式
ls -la /dev/video*
# video0, video2, video4, video6 = 真正的相机
# video1, video3, video5, video7 = 元数据设备
```

#### 5.2 创建正确的设备映射
```bash
# 创建符号链接
sudo ln -sf /dev/video0 /dev/cam0
sudo ln -sf /dev/video2 /dev/cam1  
sudo ln -sf /dev/video4 /dev/cam2
sudo ln -sf /dev/video6 /dev/cam3
```

#### 5.3 永久化配置
```bash
# 创建udev规则
sudo tee /etc/udev/rules.d/99-camera-symlinks.rules << 'EOF'
KERNEL=="video0", SYMLINK+="cam0"
KERNEL=="video2", SYMLINK+="cam1"
KERNEL=="video4", SYMLINK+="cam2"
KERNEL=="video6", SYMLINK+="cam3"
EOF

sudo udevadm control --reload-rules
sudo udevadm trigger
```

**结果**: 所有4个相机正常工作，30fps发布图像数据

---

## 📊 最终状态对比

### 磁盘使用情况
| 项目 | 修复前 | 修复后 | 释放空间 |
|------|--------|--------|----------|
| 总使用率 | 98% (93G) | 31% (29G) | 64G |
| 可用空间 | 2.1G | 66G | +63.9G |
| /home/<USER>
| /usr/share/ollama | 32G | 0G | 32G |
| /var/lib/docker | 17G | 3G | 14G |

### 服务状态
| 服务 | 修复前 | 修复后 |
|------|--------|--------|
| Docker服务 | ❌ 损坏 | ✅ 正常 |
| swarm容器 | ❌ 无法启动 | ✅ 自动运行 |
| ROS master | ❌ 网络错误 | ✅ 正常 |
| 相机节点 | ❌ 无数据 | ✅ 4个相机30fps |
| MQTT连接 | ❌ 未启动 | ✅ 自动连接 |

---

## 🔑 关键配置文件

### 1. Docker配置
**文件**: `/etc/docker/daemon.json`
```json
{
    "insecure-registries": ["*********:6000"],
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    }
}
```

### 2. 相机设备映射
**文件**: `/etc/udev/rules.d/99-camera-symlinks.rules`
```bash
KERNEL=="video0", SYMLINK+="cam0"
KERNEL=="video2", SYMLINK+="cam1"
KERNEL=="video4", SYMLINK+="cam2"
KERNEL=="video6", SYMLINK+="cam3"
```

### 3. 主机名配置
**文件**: `/etc/hosts`
```bash
127.0.0.1       VSWARM7
```

---

## 💡 经验总结

### 问题诊断技巧
1. **磁盘空间**: 使用`df -h`和`du -h --max-depth=1`定位大文件
2. **Docker问题**: 检查`docker logs`和`docker inspect`
3. **设备映射**: 使用`v4l2-ctl --list-formats-ext`验证相机设备
4. **网络问题**: 使用`ping`测试主机名解析

### 预防措施
1. **定期清理**: 设置定时任务清理日志和缓存
2. **监控磁盘**: 设置磁盘使用率告警
3. **备份配置**: 重要配置文件要备份
4. **文档记录**: 记录所有自定义配置

### 自动化建议
1. **开机自启**: 确保所有服务设置为自动启动
2. **健康检查**: 添加服务健康检查脚本
3. **故障恢复**: 编写自动故障恢复脚本

---

## 🎛️ 相机设备管理选项

### 问题描述
当Docker容器启动时，相机设备会被容器内的ROS节点占用，导致本地开发时无法访问相机设备。

### 解决方案：灵活的相机控制

#### 方案1：动态启停相机节点
```bash
# 停止容器内的相机节点（保持其他服务运行）
docker exec swarm pkill -f "usb_cam"

# 重新启动相机节点
docker exec -d swarm bash -c "source /opt/ros/noetic/setup.bash && roslaunch usb_cam vswarm-quad-360p.launch"
```

#### 方案2：创建开发模式脚本
**文件**: `/home/<USER>/docker/RobotComputingBoard/dev_mode.sh`
```bash
#!/bin/bash

case "$1" in
    "dev")
        echo "切换到开发模式 - 停止容器相机节点"
        docker exec swarm pkill -f "usb_cam"
        echo "相机设备现在可用于本地开发"
        ;;
    "prod")
        echo "切换到生产模式 - 启动容器相机节点"
        docker exec -d swarm bash -c "source /opt/ros/noetic/setup.bash && roslaunch usb_cam vswarm-quad-360p.launch"
        echo "容器相机节点已启动"
        ;;
    "status")
        echo "检查相机节点状态："
        docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rosnode list | grep cam || echo '无相机节点运行'"
        ;;
    *)
        echo "用法: $0 {dev|prod|status}"
        echo "  dev    - 开发模式（停止容器相机，释放设备）"
        echo "  prod   - 生产模式（启动容器相机）"
        echo "  status - 检查当前状态"
        ;;
esac
```

#### 方案3：修改Docker启动脚本
**选项A**: 添加环境变量控制
```bash
# 修改docker_run.sh，添加相机控制选项
--env ENABLE_CAMERAS=${ENABLE_CAMERAS:-true}
```

**选项B**: 创建两个不同的启动脚本
```bash
# docker_run_dev.sh - 开发版本（不启动相机）
# docker_run_prod.sh - 生产版本（启动相机）
```

#### 方案4：使用Docker Compose管理
**文件**: `docker-compose-dev.yml`
```yaml
version: '3'
services:
  swarm-dev:
    image: *********:6000/huabench/swarm-car
    container_name: swarm-dev
    runtime: nvidia
    # 不挂载相机设备
    volumes:
      - .:/home/<USER>
    environment:
      - ENABLE_CAMERAS=false
```

### 推荐使用方案

**推荐方案2**：创建开发模式脚本，因为：
1. **灵活性高**：可以随时切换模式
2. **简单易用**：一个命令即可切换
3. **保持其他服务**：只影响相机，其他ROS服务继续运行
4. **状态检查**：可以查看当前模式

### 使用示例
```bash
# 切换到开发模式（释放相机设备）
./dev_mode.sh dev

# 本地开发完成后，切换回生产模式
./dev_mode.sh prod

# 检查当前状态
./dev_mode.sh status
```

### 本地开发时的相机访问
```bash
# 开发模式下，可以直接访问相机
python3 -c "
import cv2
cap = cv2.VideoCapture(0)  # 现在可以正常访问
ret, frame = cap.read()
print(f'相机访问成功: {ret}')
cap.release()
"
```

---

## 🚀 验证清单

重启后检查以下项目确保一切正常：

```bash
# 1. 检查磁盘空间
df -h

# 2. 检查Docker状态
docker ps
systemctl status docker

# 3. 检查相机设备
ls -la /dev/cam*

# 4. 检查ROS话题
docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && rostopic list | grep image_raw"

# 5. 检查相机数据
docker exec swarm bash -c "source /opt/ros/noetic/setup.bash && timeout 3 rostopic hz /cam0/image_raw"
```

---

**文档创建时间**: 2025-08-19  
**系统状态**: ✅ 完全修复，所有功能正常
