from apis import *
import numpy as np

def prevent_congestion(current_velocity, surrounding_robots=None, safety_distance=0.15, adjustment_factor=0.05):
    """
    Description:
        Modifies the robot's current velocity to prevent congestion in environments with multiple robots.
        This function analyzes the positions and velocities of surrounding robots to adjust the current velocity,
        ensuring that the robot maintains a minimum safety distance. By preventing congestion, the robot can move
        more efficiently and reduce the likelihood of collisions or traffic jams in crowded environments.
    
    Parameters:
        current_velocity (numpy.ndarray): The robot's current velocity vector that needs to be adjusted.
        surrounding_robots (list, optional): A list of dictionaries containing information about surrounding robots.
            Each dictionary should include:
                - id (int): Unique identifier of the surrounding robot.
                - position (numpy.ndarray): Current position of the surrounding robot.
                - velocity (numpy.ndarray): Current velocity of the surrounding robot.
                - radius (float): Radius of the surrounding robot.
            If not provided, the function will call `get_surrounding_robots_info()` to retrieve this information.
        safety_distance (float, optional): The minimum allowed distance (in meters) between the robot and any surrounding robot.
            Default is 0.15 meters.
        adjustment_factor (float, optional): The factor by which the current velocity is adjusted to prevent congestion.
            A higher value results in a more significant adjustment. Default is 0.05.
    
    Returns:
        numpy.ndarray: The adjusted velocity vector after applying congestion prevention measures.
    """
    if surrounding_robots is None:
        surrounding_robots = get_surrounding_robots_info()
    
    adjustment_velocity = np.array([0.0, 0.0])
    self_position = get_self_position()
    self_radius = get_self_radius()
    
    for robot in surrounding_robots:
        other_position = robot['position']
        distance_vector = self_position - other_position
        distance = np.linalg.norm(distance_vector)
        min_required_distance = safety_distance + robot['radius'] + self_radius
        
        if distance < min_required_distance and distance > 0:
            # Calculate a repulsive force inversely proportional to distance
            repulsion = distance_vector / distance * (min_required_distance - distance) / min_required_distance
            adjustment_velocity += repulsion
    
    # Scale the adjustment by the adjustment_factor
    adjustment_velocity *= adjustment_factor
    
    # Combine the current velocity with the adjustment
    new_velocity = current_velocity + adjustment_velocity
    
    # Ensure the new velocity does not exceed max_speed
    max_speed = 0.2
    speed = np.linalg.norm(new_velocity)
    if speed > max_speed:
        new_velocity = new_velocity / speed * max_speed
    
    return new_velocity


def avoid_obstacles(current_velocity, obstacles_info=None, min_distance=0.15):
    """
    Description:
        Adjusts the robot's velocity to ensure a minimum distance of 0.15 meters is maintained from all surrounding obstacles.
        This function calculates repulsive vectors based on the positions and sizes of nearby obstacles and modifies the current velocity accordingly to avoid collisions.
        If no obstacles are provided, it retrieves the real-time obstacle information using the `get_surrounding_obstacles_info` API.
        The adjustment ensures smooth navigation by preventing abrupt changes in velocity, thereby enhancing the robot's ability to maneuver efficiently within the environment.
    
    Parameters:
        current_velocity (numpy.ndarray): The current velocity vector of the robot, represented as a numpy array [vx, vy].
        obstacles_info (list, optional): A list of dictionaries containing information about each obstacle.
            Each dictionary includes:
                - 'id' (int): The unique identifier of the obstacle.
                - 'position' (numpy.ndarray): The current position of the obstacle as a numpy array [x, y].
                - 'radius' (float): The radius of the obstacle.
            If not provided, the function will call `get_surrounding_obstacles_info()` to obtain this information.
        min_distance (float, optional): The minimum safe distance to maintain from each obstacle in meters. Default is 0.15.
    
    Returns:
        numpy.ndarray: The adjusted velocity vector as a numpy array [vx, vy] that ensures the robot maintains the minimum safe distance from all obstacles.
    """
    if obstacles_info is None:
        obstacles_info = get_surrounding_obstacles_info()
    
    robot_position = get_self_position()
    robot_radius = get_self_radius()
    
    total_repulsion = np.array([0.0, 0.0])
    
    for obstacle in obstacles_info:
        obstacle_position = obstacle.get('position', np.array([0.0, 0.0]))
        obstacle_radius = obstacle.get('radius', 0.0)
        
        # Vector from obstacle to robot
        vector = robot_position - obstacle_position
        distance = np.linalg.norm(vector)
        
        # Effective minimum distance considering both radii
        effective_min_distance = min_distance + robot_radius + obstacle_radius
        
        if distance < effective_min_distance and distance > 0:
            # Repulsion strength increases as the robot gets closer to the obstacle
            repulsion_strength = (effective_min_distance - distance) / distance
            repulsion_vector = (vector / distance) * repulsion_strength
            total_repulsion += repulsion_vector
    
    # Combine current velocity with total repulsion
    adjusted_velocity = current_velocity + total_repulsion
    
    # Normalize the adjusted velocity to not exceed max_speed
    speed = np.linalg.norm(adjusted_velocity)
    max_speed = 0.2  # As defined in the environment
    
    if speed > max_speed:
        adjusted_velocity = (adjusted_velocity / speed) * max_speed
    elif speed == 0:
        adjusted_velocity = np.array([0.0, 0.0])
    
    return adjusted_velocity


def combine_and_normalize_velocity(desired_velocity: np.ndarray, avoidance_velocity: np.ndarray, congestion_velocity: np.ndarray, max_speed: float = 0.2):
    """
    Description:
        Combines the desired velocity vector towards the target with adjustment vectors from 
        collision avoidance and congestion prevention to compute the final movement velocity.
        The combined velocity is then normalized to ensure it does not exceed the robot's maximum speed.
        This ensures smooth and efficient navigation while maintaining safety and avoiding overcrowding.
    
    Parameters:
        desired_velocity (numpy.ndarray): 
            The normalized velocity vector directing the robot towards its target position.
        
        avoidance_velocity (numpy.ndarray): 
            The adjustment velocity vector calculated to maintain a safe distance from other robots 
            and obstacles, ensuring collision avoidance.
        
        congestion_velocity (numpy.ndarray): 
            The adjustment velocity vector designed to prevent congestion by optimizing the 
            overall movement flow among multiple robots.
        
        max_speed (float, optional): 
            The maximum allowable speed for the robot (default is 0.2 m/s). This parameter is used 
            to normalize the final velocity vector to comply with the robot's speed constraints.
    
    Returns:
        numpy.ndarray: 
            The normalized final velocity vector that combines the desired movement with 
            necessary adjustments for collision avoidance and congestion prevention, capped 
            at the robot's maximum speed.
    """
    # Combine the desired, avoidance, and congestion velocities
    combined_velocity = desired_velocity + avoidance_velocity + congestion_velocity

    # Calculate the magnitude of the combined velocity
    speed = np.linalg.norm(combined_velocity)

    # If the combined speed exceeds the maximum speed, normalize and scale it
    if speed > max_speed:
        final_velocity = (combined_velocity / speed) * max_speed
    else:
        final_velocity = combined_velocity

    return final_velocity


def avoid_robot_collisions(current_velocity, surrounding_robots_info, min_distance=0.15, max_speed=0.2):
    """
    Description:
        Adjusts the robot's velocity to prevent collisions by ensuring a minimum distance of 0.15 meters from other robots.
        The function processes real-time information about surrounding robots to compute a new velocity vector that 
        avoids potential collisions while striving to maintain the robot's original movement direction as much as possible.
        It calculates repulsive forces or vectors from nearby robots that are within the critical distance and adjusts 
        the current velocity accordingly to navigate safely within the environment.
    
    params:
        current_velocity (numpy.ndarray): The current velocity vector of the robot (e.g., np.array([vx, vy])).
        surrounding_robots_info (list): A list of dictionaries containing information about surrounding robots.
            Each dictionary includes:
                - 'id' (int): Unique identifier of the surrounding robot.
                - 'position' (numpy.ndarray): Current position vector of the surrounding robot.
                - 'velocity' (numpy.ndarray): Current velocity vector of the surrounding robot.
                - 'radius' (float): Radius of the surrounding robot.
        min_distance (float, optional): The minimum required distance to maintain from other robots to prevent collisions. 
                                         Defaults to 0.15 meters.
        max_speed (float, optional): The maximum speed the robot can travel. Defaults to 0.2 m/s.
    
    return:
        numpy.ndarray: The adjusted velocity vector that ensures collision avoidance while maintaining optimal movement efficiency.
    """
    avoidance_velocity = np.array([0.0, 0.0])
    self_position = get_self_position()
    self_radius = get_self_radius()

    for robot_info in surrounding_robots_info:
        other_position = robot_info.get('position', np.array([0.0, 0.0]))
        other_radius = robot_info.get('radius', 0.0)

        delta_pos = self_position - other_position
        distance = np.linalg.norm(delta_pos)
        effective_min_distance = min_distance + self_radius + other_radius

        if distance < effective_min_distance and distance != 0:
            # Compute repulsive force strength inversely proportional to distance
            repulsion_strength = (effective_min_distance - distance) / distance
            repulsion_vector = (delta_pos / distance) * repulsion_strength
            avoidance_velocity += repulsion_vector

    # Combine current velocity with avoidance velocity
    adjusted_velocity = current_velocity + avoidance_velocity

    # Normalize the adjusted velocity to ensure it does not exceed max_speed
    speed = np.linalg.norm(adjusted_velocity)
    if speed > max_speed:
        adjusted_velocity = (adjusted_velocity / speed) * max_speed
    elif speed == 0:
        adjusted_velocity = np.array([0.0, 0.0])

    return adjusted_velocity


def avoid_obstacle_collisions(current_velocity, obstacles_info, min_distance=0.15, robot_radius=None, max_speed=0.2, environment_bounds=None):
    """
    Description:
        Adjust the robot's velocity to maintain a minimum distance from all detected obstacles, thereby preventing collisions.
        This function analyzes the positions and velocities of surrounding obstacles relative to the robot's current velocity and adjusts the velocity vector accordingly.
        It ensures that the robot steers away from obstacles that are within the specified minimum distance by calculating repulsive vectors and combining them with the current velocity.
        The adjusted velocity is then normalized to ensure it does not exceed the robot's maximum speed and remains within the defined environment boundaries.
    
    params:
        current_velocity (numpy.ndarray): The current velocity vector of the robot that needs to be adjusted to avoid collisions.
        obstacles_info (list): A list of dictionaries containing real-time information about each obstacle. Each dictionary includes:
            - id (int): The unique identifier of the obstacle.
            - position (numpy.ndarray): The current position vector of the obstacle.
            - velocity (numpy.ndarray): The current velocity vector of the obstacle.
            - radius (float): The radius of the obstacle.
        min_distance (float, optional): The minimum required distance to maintain from any obstacle to avoid collisions. Defaults to 0.15 meters.
        robot_radius (float, optional): The radius of the robot itself, used to calculate the effective minimum distance considering both robot and obstacle sizes.
        max_speed (float, optional): The maximum speed at which the robot can travel. Ensures that the adjusted velocity does not exceed this limit. Defaults to 0.2 m/s.
        environment_bounds (dict, optional): The boundaries of the operational environment, containing:
            - 'x_min' (float): Minimum x-coordinate boundary.
            - 'x_max' (float): Maximum x-coordinate boundary.
            - 'y_min' (float): Minimum y-coordinate boundary.
            - 'y_max' (float): Maximum y-coordinate boundary.
        Ensures the adjusted velocity keeps the robot within these bounds.
    
    return:
        numpy.ndarray: The adjusted and normalized velocity vector that ensures the robot maintains the minimum distance from all obstacles while adhering to speed and boundary constraints.
    """
    
    # Retrieve robot radius if not provided
    if robot_radius is None:
        robot_radius = get_self_radius()
        if robot_radius is None:
            robot_radius = 0.0  # Default to 0 if API fails
    
    # Set default environment bounds if not provided
    if environment_bounds is None:
        environment_bounds = {'x_min': -2.5, 'x_max': 2.5, 'y_min': -2.5, 'y_max': 2.5}
    
    # Initialize repulsion vector
    repulsion = np.array([0.0, 0.0])
    
    # Get current position of the robot
    self_position = get_self_position()
    if self_position is None:
        self_position = np.array([0.0, 0.0])  # Default to origin if API fails
    
    # Calculate repulsion from each obstacle
    for obstacle in obstacles_info:
        obstacle_position = obstacle.get('position', np.array([0.0, 0.0]))
        obstacle_radius = obstacle.get('radius', 0.0)
        
        # Compute vector from obstacle to robot
        vector = self_position - obstacle_position
        distance = np.linalg.norm(vector)
        effective_min_distance = min_distance + robot_radius + obstacle_radius
        
        if distance < effective_min_distance and distance > 0:
            # Normalize the vector and scale repulsion inversely by distance
            repulsion += (vector / distance) * (effective_min_distance - distance) / effective_min_distance
    
    # Combine current velocity with repulsion
    adjusted_velocity = current_velocity + repulsion
    
    # Ensure the adjusted velocity does not exceed max_speed
    speed = np.linalg.norm(adjusted_velocity)
    if speed > max_speed and speed > 0:
        adjusted_velocity = (adjusted_velocity / speed) * max_speed
    
    # Ensure the robot stays within environment bounds
    next_position = self_position + adjusted_velocity / 10  # Assuming control update at 10Hz
    if next_position[0] < environment_bounds['x_min'] + min_distance + robot_radius:
        adjusted_velocity[0] = max(adjusted_velocity[0], 0.0)
    elif next_position[0] > environment_bounds['x_max'] - min_distance - robot_radius:
        adjusted_velocity[0] = min(adjusted_velocity[0], 0.0)
    
    if next_position[1] < environment_bounds['y_min'] + min_distance + robot_radius:
        adjusted_velocity[1] = max(adjusted_velocity[1], 0.0)
    elif next_position[1] > environment_bounds['y_max'] - min_distance - robot_radius:
        adjusted_velocity[1] = min(adjusted_velocity[1], 0.0)
    
    # Re-normalize after boundary adjustments
    speed = np.linalg.norm(adjusted_velocity)
    if speed > max_speed and speed > 0:
        adjusted_velocity = (adjusted_velocity / speed) * max_speed
    
    return adjusted_velocity


def avoid_other_robots(
    current_velocity: np.ndarray,
    self_position: np.ndarray = None,
    surrounding_robots: list = None,
    self_radius: float = 0.0,
    min_distance: float = 0.15
):
    """
    Description:
        Adjusts the robot's velocity to maintain a minimum distance from other robots, thereby preventing collisions.
        The function calculates repulsion vectors from each surrounding robot that is within the minimum distance threshold
        and adjusts the current velocity accordingly to steer away from potential collisions. It ensures that the adjusted
        velocity does not exceed the robot's maximum speed.
    
    Parameters:
        current_velocity (numpy.ndarray): The robot's current velocity vector.
        self_position (numpy.ndarray, optional): The current position of the robot. If not provided, it retrieves the position using get_self_position().
        surrounding_robots (list, optional): A list of dictionaries containing information about surrounding robots.
            Each dictionary should have keys: 'id', 'position', 'velocity', and 'radius'. If not provided, it retrieves the information using get_surrounding_robots_info().
        self_radius (float, optional): The radius of the robot. If not provided, it retrieves the radius using get_self_radius().
        min_distance (float, optional): The minimum required distance to maintain from other robots to avoid collisions. Default is 0.15 meters.
    
    Returns:
        numpy.ndarray: The adjusted velocity vector that ensures the robot maintains the minimum distance from other robots.
    """
    # Retrieve self position if not provided
    if self_position is None:
        self_position = get_self_position()
    
    # Retrieve surrounding robots info if not provided
    if surrounding_robots is None:
        surrounding_robots = get_surrounding_robots_info()
    
    # Retrieve self radius if not provided
    if self_radius == 0.0:
        self_radius = get_self_radius()
    
    adjustment_velocity = np.array([0.0, 0.0])
    max_speed = 0.2  # Maximum speed in m/s
    
    for robot in surrounding_robots:
        robot_position = robot.get('position', np.array([0.0, 0.0]))
        robot_radius = robot.get('radius', 0.0)
        
        # Compute the vector from the other robot to self
        vec_to_self = self_position - robot_position
        distance = np.linalg.norm(vec_to_self)
        
        # Calculate the combined minimum safe distance
        safe_distance = self_radius + robot_radius + min_distance
        
        if distance < safe_distance and distance > 0:
            # Normalize the vector
            direction = vec_to_self / distance
            # Compute repulsion strength inversely proportional to distance
            repulsion_strength = (safe_distance - distance) / safe_distance
            # Scale the repulsion vector
            repulsion = direction * repulsion_strength
            # Accumulate the repulsion vectors
            adjustment_velocity += repulsion
    
    # Combine current velocity with adjustment
    new_velocity = current_velocity + adjustment_velocity
    
    # Ensure the new velocity does not exceed max_speed
    speed = np.linalg.norm(new_velocity)
    if speed > max_speed and speed > 0:
        new_velocity = (new_velocity / speed) * max_speed
    
    return new_velocity


def run_loop():
    '''
    Description: An interface function for users to call, based on existing functions written by other assistants. Users only need to call this function to complete the predetermined task.

    params:
        None
    return:
        None
    '''
    while True:
        navigate_to_target()


def navigate_to_target(target_position: np.ndarray=None, current_position: np.ndarray=None, current_velocity: np.ndarray=None, surrounding_robots_info: list=None, obstacles_info: list=None, min_distance: float=0.15, collision_adjustment: np.ndarray=None, congestion_adjustment: np.ndarray=None, max_speed: float=0.2, environment_bounds: dict={'x_min': -2.5, 'x_max': 2.5, 'y_min': -2.5, 'y_max': 2.5}):
    """
    Description:
        Main function to navigate the robot towards the target position while avoiding collisions with other robots and obstacles,
        and preventing congestion within the environment. This function integrates target acquisition, collision avoidance, and
        congestion prevention mechanisms to compute and set the robot's final velocity. It ensures that the robot moves efficiently
        towards its goal without exceeding the maximum speed limit or violating environment boundaries.
    
    Parameters:
        target_position (numpy.ndarray, optional): 
            The coordinates of the target position the robot aims to reach. 
            If not provided, the function will retrieve it using `get_target_position()`.
        
        current_position (numpy.ndarray, optional): 
            The current coordinates of the robot. 
            If not provided, the function will retrieve it using `get_self_position()`.
        
        current_velocity (numpy.ndarray, optional): 
            The current velocity vector of the robot. 
            If not provided, the function will retrieve it using `get_self_velocity()`.
        
        surrounding_robots_info (list, optional): 
            A list of dictionaries containing information about surrounding robots. 
            Each dictionary should include:
                - 'id' (int): Unique identifier of the surrounding robot.
                - 'position' (numpy.ndarray): Current position of the surrounding robot.
                - 'velocity' (numpy.ndarray): Current velocity of the surrounding robot.
                - 'radius' (float): Radius of the surrounding robot.
            If not provided, the function will retrieve it using `get_surrounding_robots_info()`.
        
        obstacles_info (list, optional): 
            A list of dictionaries containing information about surrounding obstacles. 
            Each dictionary should include:
                - 'id' (int): Unique identifier of the obstacle.
                - 'position' (numpy.ndarray): Current position of the obstacle.
                - 'radius' (float): Radius of the obstacle.
            If not provided, the function will retrieve it using `get_surrounding_obstacles_info()`.
        
        min_distance (float, optional): 
            The minimum required distance to maintain from other robots and obstacles to prevent collisions. 
            Default is 0.15 meters.
        
        collision_adjustment (numpy.ndarray, optional): 
            The velocity adjustment vector obtained from collision avoidance algorithms to prevent collisions 
            with other robots or obstacles. If not provided, it will be calculated within the function.
        
        congestion_adjustment (numpy.ndarray, optional): 
            The velocity adjustment vector obtained from congestion prevention algorithms to avoid traffic 
            bottlenecks and enhance movement efficiency. If not provided, it will be calculated within the function.
        
        max_speed (float, optional): 
            The maximum speed the robot can attain. Ensures that the final velocity does not exceed this limit. 
            Defaults to 0.2 m/s.
        
        environment_bounds (dict, optional): 
            The boundaries of the operational environment, containing:
                - 'x_min' (float): Minimum x-coordinate boundary.
                - 'x_max' (float): Maximum x-coordinate boundary.
                - 'y_min' (float): Minimum y-coordinate boundary.
                - 'y_max' (float): Maximum y-coordinate boundary.
            Ensures the robot remains within these bounds during navigation. Defaults to {'x_min': -2.5, 'x_max': 2.5, 'y_min': -2.5, 'y_max': 2.5}.
    
    Returns:
        None
            This function does not return any value. It computes the final velocity vector based on the target position and 
            adjustments for collision avoidance and congestion prevention, then sets this velocity using the `set_self_velocity()` API.
    """

    # Retrieve target position if not provided
    if target_position is None:
        target_position = get_target_position()
        if target_position is None:
            target_position = np.array([0.0, 0.0])

    # Retrieve current position if not provided
    if current_position is None:
        current_position = get_self_position()
        if current_position is None:
            current_position = np.array([0.0, 0.0])

    # Retrieve current velocity if not provided
    if current_velocity is None:
        current_velocity = get_self_velocity()
        if current_velocity is None:
            current_velocity = np.array([0.0, 0.0])

    # Retrieve surrounding robots info if not provided
    if surrounding_robots_info is None:
        surrounding_robots_info = get_surrounding_robots_info()
        if surrounding_robots_info is None:
            surrounding_robots_info = []

    # Retrieve obstacles info if not provided
    if obstacles_info is None:
        obstacles_info = get_surrounding_obstacles_info()
        if obstacles_info is None:
            obstacles_info = []

    # Calculate collision adjustment if not provided
    if collision_adjustment is None:
        # Adjust for robot collisions
        collision_adjust_robot = avoid_robot_collisions(current_velocity, surrounding_robots_info, min_distance, max_speed)
        # Adjust for obstacle collisions
        collision_adjust_obstacle = avoid_obstacle_collisions(current_velocity, obstacles_info, min_distance, get_self_radius(), max_speed, environment_bounds)
        # Combine both adjustments
        collision_adjustment = collision_adjust_robot + collision_adjust_obstacle

    # Calculate congestion adjustment if not provided
    if congestion_adjustment is None:
        congestion_adjustment = prevent_congestion(current_velocity, surrounding_robots_info, min_distance, adjustment_factor=0.05)

    # Calculate the desired final velocity towards the target with adjustments
    final_velocity = calculate_target_velocity(
        target_position=target_position,
        current_position=current_position,
        max_speed=max_speed,
        collision_avoidance_adjustment=collision_adjustment,
        congestion_prevention_adjustment=congestion_adjustment
    )

    # Ensure the final velocity does not exceed max_speed
    speed = np.linalg.norm(final_velocity)
    if speed > max_speed and speed > 0:
        final_velocity = (final_velocity / speed) * max_speed
    elif speed == 0:
        final_velocity = np.array([0.0, 0.0])

    # Set the final velocity to the robot
    set_self_velocity(final_velocity)


def calculate_desired_velocity(current_position, target_position, max_speed=0.2, current_velocity=None):
    """
    Description:
        Calculates the desired velocity vector that directs the robot towards its target position. 
        The function computes the direction from the current position to the target, normalizes this vector, 
        and scales it by the robot's maximum speed to ensure consistent movement. 
        Additionally, it considers the robot's current velocity to allow for smooth acceleration and deceleration, 
        preventing abrupt changes in movement that could lead to inefficiencies or collisions.

    params:
        current_position (numpy.ndarray): The current real-time position of the robot in the environment.
        target_position (numpy.ndarray): The target position that the robot aims to reach.
        max_speed (float, optional): The maximum speed (in m/s) that the robot can attain. Defaults to 0.2 m/s.
        current_velocity (numpy.ndarray, optional): The current velocity vector of the robot, used to adjust the desired velocity smoothly.
            Defaults to np.array([0.0, 0.0]).
    
    return:
        numpy.ndarray: The normalized desired velocity vector scaled to the robot's maximum speed, guiding it towards the target.
    """
    if current_velocity is None:
        current_velocity = np.array([0.0, 0.0])

    direction_vector = target_position - current_position
    distance = np.linalg.norm(direction_vector)
    
    if distance < 1e-6:
        desired_velocity = np.array([0.0, 0.0])
    else:
        direction_unit_vector = direction_vector / distance
        desired_velocity = direction_unit_vector * max_speed
    
    # Smooth velocity adjustment
    blend_factor = 0.5  # Determines the influence of current_velocity vs desired_velocity
    final_velocity = (desired_velocity * blend_factor) + (current_velocity * (1 - blend_factor))
    
    final_speed = np.linalg.norm(final_velocity)
    if final_speed > max_speed:
        final_velocity = (final_velocity / final_speed) * max_speed
    
    return final_velocity


def calculate_target_velocity(target_position, current_position, max_speed=0.2, collision_avoidance_adjustment=None, congestion_prevention_adjustment=None):
    """
    Description:
        Calculate the normalized velocity vector directing the robot towards its target position while considering collision avoidance and congestion prevention adjustments.
        This function computes the desired velocity based on the target position and current position, integrates adjustments from collision avoidance and congestion prevention mechanisms,
        and ensures that the final velocity does not exceed the robot's maximum speed.

    params:
        target_position (numpy.ndarray): The coordinates of the target position the robot aims to reach.
        current_position (numpy.ndarray): The current coordinates of the robot.
        max_speed (float, optional): The maximum speed the robot can attain. Defaults to 0.2 m/s.
        collision_avoidance_adjustment (numpy.ndarray, optional): The velocity adjustment vector obtained from collision avoidance algorithms to prevent collisions with other robots or obstacles.
        congestion_prevention_adjustment (numpy.ndarray, optional): The velocity adjustment vector obtained from congestion prevention algorithms to avoid traffic bottlenecks and enhance movement efficiency.

    return:
        numpy.ndarray: The normalized final velocity vector that directs the robot towards the target while incorporating necessary adjustments for safe and efficient navigation.
    """
    # Initialize collision avoidance adjustment if not provided
    if collision_avoidance_adjustment is None:
        collision_avoidance_adjustment = np.array([0.0, 0.0])

    # Initialize congestion prevention adjustment if not provided
    if congestion_prevention_adjustment is None:
        congestion_prevention_adjustment = np.array([0.0, 0.0])

    # Get the current velocity of the robot
    current_velocity = get_self_velocity()

    # Calculate desired velocity towards the target
    desired_velocity = calculate_desired_velocity(
        current_position=current_position,
        target_position=target_position,
        max_speed=max_speed,
        current_velocity=current_velocity
    )

    # Combine desired velocity with collision avoidance and congestion prevention adjustments
    final_velocity = combine_and_normalize_velocity(
        desired_velocity=desired_velocity,
        avoidance_velocity=collision_avoidance_adjustment,
        congestion_velocity=congestion_prevention_adjustment,
        max_speed=max_speed
    )

    return final_velocity


def set_final_velocity(final_velocity: np.ndarray, adjustment: np.ndarray = np.array([0.0, 0.0]), max_speed: float = 0.2):
    """
    Description:
        Sets the computed and normalized final velocity to the robot. This function takes the desired velocity vector, 
        applies necessary adjustments for collision avoidance and congestion prevention, ensures the velocity does not 
        exceed the maximum allowed speed, and then updates the robot's velocity using the provided velocity control API.
        It integrates with other components to maintain smooth and safe navigation within the environment bounds.

    Parameters:
        final_velocity (numpy.ndarray): The desired velocity vector calculated to direct the robot towards its target position.
        adjustment (numpy.ndarray, optional): The velocity adjustment vector obtained from collision avoidance and congestion prevention mechanisms. Defaults to np.array([0.0, 0.0]).
        max_speed (float, optional): The maximum speed limit for the robot. Ensures the final velocity does not exceed this threshold. Defaults to 0.2.

    Returns:
        None: This function does not return any value. It directly updates the robot's velocity.
    """

    # Combine the final velocity with the adjustment
    combined_velocity = final_velocity + adjustment

    # Calculate the magnitude of the combined velocity
    speed = np.linalg.norm(combined_velocity)

    # If the speed exceeds max_speed and is not zero, normalize to max_speed
    if speed > max_speed and speed != 0:
        normalized_velocity = (combined_velocity / speed) * max_speed
    else:
        normalized_velocity = combined_velocity

    # Ensure the velocity does not cause the robot to exceed environment bounds
    current_position = get_self_position()
    predicted_position = current_position + normalized_velocity * 0.1  # Assuming update rate of 10Hz (0.1s per update)
    environment_bounds = {'x_min': -2.5, 'x_max': 2.5, 'y_min': -2.5, 'y_max': 2.5}
    robot_radius = get_self_radius()

    # Adjust velocity if predicted position is out of bounds
    if predicted_position[0] - robot_radius < environment_bounds['x_min']:
        normalized_velocity[0] = abs(normalized_velocity[0])
    elif predicted_position[0] + robot_radius > environment_bounds['x_max']:
        normalized_velocity[0] = -abs(normalized_velocity[0])

    if predicted_position[1] - robot_radius < environment_bounds['y_min']:
        normalized_velocity[1] = abs(normalized_velocity[1])
    elif predicted_position[1] + robot_radius > environment_bounds['y_max']:
        normalized_velocity[1] = -abs(normalized_velocity[1])

    # Normalize again after boundary adjustments
    speed = np.linalg.norm(normalized_velocity)
    if speed > max_speed and speed != 0:
        normalized_velocity = (normalized_velocity / speed) * max_speed

    # Set the robot's velocity using the provided API
    set_self_velocity(normalized_velocity)
