from apis import initialize_ros_node, get_self_position,set_self_velocity,get_self_radius,get_surrounding_environment_info,get_environment_range,get_self_velocity,get_assigned_task
import numpy as np
import time

def calculate_average_position(surrounding_info, self_position):
    """
    Calculates the average position of neighboring robots to ensure cohesion within the flock.
    The robot moves towards this average position to stay connected with the group.

    Parameters:
        surrounding_info (list of dict): 
            A list containing information about surrounding objects within the perception range.
            Each dictionary in the list includes:
                - "Type" (str): Indicates whether the object is a 'robot' or an 'obstacle'.
                - "position" (numpy.ndarray): The current position of the object.
                - "velocity" (numpy.ndarray): The current velocity of the object.
                - "radius" (float): The radius of the object.
        self_position (numpy.ndarray): 
            The current position of the robot itself.

    Returns:
        numpy.ndarray: 
            A NumPy array representing the average position of neighboring robots. 
            If no neighboring robots are detected, returns the robot's current position.
    """
    robot_positions = [
        obj["position"] for obj in surrounding_info if obj.get("Type") == "robot"
    ]
    
    if robot_positions:
        average_position = np.mean(robot_positions, axis=0)
        return average_position
    else:
        return self_position


def calculate_average_velocity(surrounding_info, min_neighbors=1):
    """
    Description:
        Calculates the average velocity of neighboring robots to ensure alignment within the flock.
        This function processes the surrounding environment information to extract the velocities of nearby robots
        and computes their average. If the number of neighboring robots is below the specified minimum,
        the function returns a zero velocity vector, indicating no alignment adjustment is necessary.
    
    Params:
        surrounding_info (list of dict): 
            A list containing information about surrounding objects within the perception range.
            Each dictionary in the list should have the following keys:
                - "Type" (str): Indicates whether the object is a 'robot' or an 'obstacle'.
                - "velocity" (numpy.ndarray): The current velocity of the object. For obstacles, this is [0, 0].
                - "position" (numpy.ndarray): The current position of the object.
                - "radius" (float): The radius of the object.
        min_neighbors (int, optional): 
            The minimum number of neighboring robots required to compute a meaningful average velocity.
            Defaults to 1.
    
    Returns:
        numpy.ndarray: 
            A 2D vector representing the average velocity of neighboring robots. If there are fewer than
            `min_neighbors` neighboring robots, returns a zero vector [0.0, 0.0].
    """
    sum_velocity = np.array([0.0, 0.0])
    robot_count = 0

    for obj in surrounding_info:
        if obj.get("Type") == "robot":
            velocity = obj.get("velocity", np.array([0.0, 0.0]))
            sum_velocity += velocity
            robot_count += 1

    if robot_count >= min_neighbors:
        average_velocity = sum_velocity / robot_count
        norm = np.linalg.norm(average_velocity)
        if norm > 0:
            normalized_average_velocity = average_velocity / norm
            return normalized_average_velocity
    return np.array([0.0, 0.0])


def calculate_separation_vector(self_position, self_radius, surrounding_objects, distance_threshold, environment_range=None):
    """
    Determines a separation vector to maintain a safe distance from other robots and obstacles, preventing collisions.
    
    This function calculates a vector that directs the robot away from nearby objects that are within a critical proximity.
    The critical proximity is defined as the sum of the robot's radius and the neighboring object's radius plus a specified distance threshold.
    The separation vector is the summation of individual repulsion vectors from each nearby object, inversely scaled by their distance to ensure stronger repulsion when closer.
    Optionally, the function can consider the environment boundaries to prevent the robot from moving out of the designated area.
    
    Parameters:
        self_position (numpy.ndarray): The current position of the robot as a 2D coordinate (x, y).
        self_radius (float): The radius of the robot.
        surrounding_objects (list): A list of dictionaries, each containing information about a surrounding object with the following keys:
            - "Type" (str): Indicates whether the object is a 'robot' or 'obstacle'.
            - "position" (numpy.ndarray): The current position of the object as a 2D coordinate (x, y).
            - "velocity" (numpy.ndarray): The current velocity of the object. For obstacles, this is [0, 0].
            - "radius" (float): The radius of the object.
        distance_threshold (float): The additional safety distance to maintain beyond the sum of radii to prevent collisions.
        environment_range (dict, optional): A dictionary containing the x and y boundaries of the environment with the following keys:
            - x_min (float): The minimum x-value of the environment.
            - x_max (float): The maximum x-value of the environment.
            - y_min (float): The minimum y-value of the environment.
            - y_max (float): The maximum y-value of the environment.
            Default is None, meaning boundary conditions are not considered.
    
    Returns:
        numpy.ndarray: A 2D vector representing the calculated separation force to be applied to the robot's velocity.
    """
    separation_vector = np.array([0.0, 0.0])
    for obj in surrounding_objects:
        obj_position = obj.get("position", np.array([0.0, 0.0]))
        obj_radius = obj.get("radius", 0.0)
        distance_threshold_total = self_radius + obj_radius + distance_threshold
        vector_to_obj = self_position - obj_position
        distance = np.linalg.norm(vector_to_obj)
        if distance < distance_threshold_total and distance > 1e-5:
            repulsion_strength = 1.0 / distance
            repulsion_vector = vector_to_obj / distance * repulsion_strength
            separation_vector += repulsion_vector
    if environment_range:
        x_min = environment_range.get("x_min", -np.inf)
        x_max = environment_range.get("x_max", np.inf)
        y_min = environment_range.get("y_min", -np.inf)
        y_max = environment_range.get("y_max", np.inf)
        # Left boundary
        distance_left = self_position[0] - x_min
        if distance_left < self_radius + distance_threshold and distance_left > 1e-5:
            separation_vector[0] += (self_position[0] - x_min) / distance_left * (1.0 / distance_left)
        # Right boundary
        distance_right = x_max - self_position[0]
        if distance_right < self_radius + distance_threshold and distance_right > 1e-5:
            separation_vector[0] -= (x_max - self_position[0]) / distance_right * (1.0 / distance_right)
        # Bottom boundary
        distance_bottom = self_position[1] - y_min
        if distance_bottom < self_radius + distance_threshold and distance_bottom > 1e-5:
            separation_vector[1] += (self_position[1] - y_min) / distance_bottom * (1.0 / distance_bottom)
        # Top boundary
        distance_top = y_max - self_position[1]
        if distance_top < self_radius + distance_threshold and distance_top > 1e-5:
            separation_vector[1] -= (y_max - self_position[1]) / distance_top * (1.0 / distance_top)
    norm = np.linalg.norm(separation_vector)
    if norm > 0:
        separation_vector = separation_vector / norm
    return separation_vector


def compute_combined_velocity(surrounding_info: list, self_position: np.ndarray, self_radius: float, distance_threshold: float, environment_range: dict=None, cohesion_weight: float=1.0, alignment_weight: float=1.0, separation_weight: float=1.5):
    """
    Description:
        Computes the final velocity for the robot by combining the cohesion, alignment, and separation vectors.
        - **Cohesion**: Directs the robot towards the average position of neighboring robots to maintain group cohesion.
        - **Alignment**: Adjusts the robot's velocity to align with the average velocity of neighboring robots, ensuring synchronized movement.
        - **Separation**: Generates a repulsion vector to maintain a safe distance from other robots and obstacles, preventing collisions.
        The combined velocity is a weighted sum of these three vectors, ensuring the robot moves cohesively with the flock while avoiding conflicts.
        The final velocity is normalized to adhere to the robot's maximum speed constraint.
    
    Parameters:
        surrounding_info (list of dict): 
            Real-time information about surrounding robots and obstacles obtained from `get_surrounding_environment_info()`.
            Each dictionary contains:
                - "Type" (str): 'robot' or 'obstacle'.
                - "position" (numpy.ndarray): Current position of the object.
                - "velocity" (numpy.ndarray): Current velocity of the object. For obstacles, this is [0.0, 0.0].
                - "radius" (float): Radius of the object.
        self_position (numpy.ndarray): 
            The robot's current position in the environment, obtained from `get_self_position()`.
        self_radius (float): 
            The radius of the robot, obtained from `get_self_radius()`.
        distance_threshold (float): 
            Additional safety distance to maintain beyond the sum of radii to prevent collisions.
        environment_range (dict, optional): 
            Boundaries of the environment obtained from `get_environment_range()`. Contains:
                - "x_min" (float): Minimum x-coordinate.
                - "x_max" (float): Maximum x-coordinate.
                - "y_min" (float): Minimum y-coordinate.
                - "y_max" (float): Maximum y-coordinate.
            Default is None, meaning boundary conditions are not considered.
        cohesion_weight (float, optional): 
            Weight factor for the cohesion vector to prioritize group connectivity.
            Defaults to 1.0.
        alignment_weight (float, optional): 
            Weight factor for the alignment vector to prioritize synchronized movement.
            Defaults to 1.0.
        separation_weight (float, optional): 
            Weight factor for the separation vector to prioritize collision avoidance.
            Defaults to 1.5.
    
    Returns:
        numpy.ndarray: 
            A 2D velocity vector representing the combined and weighted velocity for the robot.
            This velocity ensures the robot moves cohesively with the flock, aligns with neighboring movements,
            and maintains a safe distance from other objects within the environment.
    """
    # Calculate cohesion vector
    average_position = calculate_average_position(surrounding_info, self_position)
    cohesion_vector = average_position - self_position
    cohesion_norm = np.linalg.norm(cohesion_vector)
    if cohesion_norm > 0:
        cohesion_vector = cohesion_vector / cohesion_norm
    else:
        cohesion_vector = np.array([0.0, 0.0])
    
    # Calculate alignment vector
    average_velocity = calculate_average_velocity(surrounding_info)
    alignment_norm = np.linalg.norm(average_velocity)
    if alignment_norm > 0:
        alignment_vector = average_velocity / alignment_norm
    else:
        alignment_vector = np.array([0.0, 0.0])
    
    # Calculate separation vector
    separation_vector = calculate_separation_vector(
        self_position,
        self_radius,
        surrounding_info,
        distance_threshold,
        environment_range
    )
    
    # Combine the vectors with respective weights
    combined_velocity = (cohesion_weight * cohesion_vector) + \
                        (alignment_weight * alignment_vector) + \
                        (separation_weight * separation_vector)
    
    # Normalize the combined velocity to respect the maximum speed
    max_speed = 0.2  # meters per second
    combined_norm = np.linalg.norm(combined_velocity)
    if combined_norm > 0:
        normalized_velocity = (combined_velocity / combined_norm) * min(combined_norm, max_speed)
    else:
        normalized_velocity = np.array([0.0, 0.0])
    
    return normalized_velocity


def update_robot_velocity(surrounding_info: list, self_position: np.ndarray, self_radius: float, distance_threshold: float, environment_range: dict=None, cohesion_weight: float=1.0, alignment_weight: float=1.0, separation_weight: float=1.5):
    """
    Description:
        Updates the robot's velocity based on combined behavioral vectors to ensure cohesive movement within a flock.
        The function integrates cohesion, alignment, and separation behaviors to calculate an optimal velocity vector
        that maintains group connectivity, synchronizes movement with neighboring robots, and prevents collisions 
        with other robots and obstacles. This velocity is then set using the `set_self_velocity` API to direct the 
        robot's movement autonomously.
    
    Parameters:
        surrounding_info (list of dict):
            Real-time information about surrounding robots and obstacles obtained from `get_surrounding_environment_info()`.
            Each dictionary in the list contains:
                - "Type" (str): Indicates whether the object is a 'robot' or 'obstacle'.
                - "position" (numpy.ndarray): The current position of the object.
                - "velocity" (numpy.ndarray): The current velocity of the object. For obstacles, this is [0.0, 0.0].
                - "radius" (float): The radius of the object.
        
        self_position (numpy.ndarray):
            The current position of the robot itself in the environment, obtained from `get_self_position()`.
        
        self_radius (float):
            The radius of the robot itself, obtained from `get_self_radius()`.
        
        distance_threshold (float):
            Additional safety distance to maintain beyond the sum of radii to prevent collisions. This ensures that the 
            robot maintains a minimum safe distance from other objects.
        
        environment_range (dict, optional):
            Boundaries of the environment obtained from `get_environment_range()`. Contains:
                - "x_min" (float): The minimum x-coordinate of the environment.
                - "x_max" (float): The maximum x-coordinate of the environment.
                - "y_min" (float): The minimum y-coordinate of the environment.
                - "y_max" (float): The maximum y-coordinate of the environment.
            Default is None, meaning boundary conditions are not considered.
        
        cohesion_weight (float, optional):
            Weight factor for the cohesion vector to prioritize group connectivity. Determines the influence of moving 
            towards the average position of neighboring robots. Higher values increase the tendency to stay close to the group.
            Defaults to 1.0.
        
        alignment_weight (float, optional):
            Weight factor for the alignment vector to prioritize synchronized movement with neighboring robots. Determines 
            the influence of matching the average velocity of the flock. Higher values enhance cohesion in movement direction.
            Defaults to 1.0.
        
        separation_weight (float, optional):
            Weight factor for the separation vector to prioritize collision avoidance. Determines the influence of maintaining 
            a safe distance from other objects. Higher values increase the robot's responsiveness to nearby objects.
            Defaults to 1.5.
    
    Returns:
        None
    """
    combined_velocity = compute_combined_velocity(
        surrounding_info=surrounding_info,
        self_position=self_position,
        self_radius=self_radius,
        distance_threshold=distance_threshold,
        environment_range=environment_range,
        cohesion_weight=cohesion_weight,
        alignment_weight=alignment_weight,
        separation_weight=separation_weight
    )
    set_self_velocity(combined_velocity)


def run_loop():
    '''
    Description: An interface function for users to call, based on existing functions written by other assistants. Users only need to call this function to complete the predetermined task.

    params:
        None
    return:
        None
    '''
    distance_threshold = 0.1  # meters
    while True:
        surrounding_info = get_surrounding_environment_info()
        self_position = get_self_position()
        self_radius = get_self_radius()
        environment_range = get_environment_range()
        update_robot_velocity(
            surrounding_info=surrounding_info,
            self_position=self_position,
            self_radius=self_radius,
            distance_threshold=distance_threshold,
            environment_range=environment_range
        )
        time.sleep(0.01)
