# generated from genmsg/cmake/pkg-genmsg.cmake.em

message(STATUS "code_llm: 2 messages, 5 services")

set(MSG_I_FLAGS "-Icode_llm:/catkin_ws/src/code_llm/msg;-Istd_msgs:/opt/ros/noetic/share/std_msgs/cmake/../msg;-Igeometry_msgs:/opt/ros/noetic/share/geometry_msgs/cmake/../msg")

# Find all generators
find_package(gencpp REQUIRED)
find_package(geneus REQUIRED)
find_package(genlisp REQUIRED)
find_package(gennodejs REQUIRED)
find_package(genpy REQUIRED)

add_custom_target(code_llm_generate_messages ALL)

# verify that message/service dependencies have not changed since configure



get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/msg/Observations.msg" "geometry_msgs/Point:geometry_msgs/Vector3:geometry_msgs/Twist:code_llm/ObjInfo"
)

get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/msg/ObjInfo.msg" "geometry_msgs/Point:geometry_msgs/Vector3:geometry_msgs/Twist"
)

get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" "geometry_msgs/Point:geometry_msgs/Vector3:geometry_msgs/Twist:code_llm/ObjInfo"
)

get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" "geometry_msgs/Point"
)

get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" ""
)

get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" ""
)

get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_custom_target(_code_llm_generate_messages_check_deps_${_filename}
  COMMAND ${CATKIN_ENV} ${PYTHON_EXECUTABLE} ${GENMSG_CHECK_DEPS_SCRIPT} "code_llm" "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" ""
)

#
#  langs = gencpp;geneus;genlisp;gennodejs;genpy
#

### Section generating for lang: gencpp
### Generating Messages
_generate_msg_cpp(code_llm
  "/catkin_ws/src/code_llm/msg/Observations.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)
_generate_msg_cpp(code_llm
  "/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)

### Generating Services
_generate_srv_cpp(code_llm
  "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)
_generate_srv_cpp(code_llm
  "/catkin_ws/src/code_llm/srv/GetCharPoints.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg"
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)
_generate_srv_cpp(code_llm
  "/catkin_ws/src/code_llm/srv/ConnectEntities.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)
_generate_srv_cpp(code_llm
  "/catkin_ws/src/code_llm/srv/StartEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)
_generate_srv_cpp(code_llm
  "/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
)

### Generating Module File
_generate_module_cpp(code_llm
  ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
  "${ALL_GEN_OUTPUT_FILES_cpp}"
)

add_custom_target(code_llm_generate_messages_cpp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_cpp}
)
add_dependencies(code_llm_generate_messages code_llm_generate_messages_cpp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_cpp _code_llm_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(code_llm_gencpp)
add_dependencies(code_llm_gencpp code_llm_generate_messages_cpp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS code_llm_generate_messages_cpp)

### Section generating for lang: geneus
### Generating Messages
_generate_msg_eus(code_llm
  "/catkin_ws/src/code_llm/msg/Observations.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)
_generate_msg_eus(code_llm
  "/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)

### Generating Services
_generate_srv_eus(code_llm
  "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)
_generate_srv_eus(code_llm
  "/catkin_ws/src/code_llm/srv/GetCharPoints.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg"
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)
_generate_srv_eus(code_llm
  "/catkin_ws/src/code_llm/srv/ConnectEntities.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)
_generate_srv_eus(code_llm
  "/catkin_ws/src/code_llm/srv/StartEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)
_generate_srv_eus(code_llm
  "/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
)

### Generating Module File
_generate_module_eus(code_llm
  ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
  "${ALL_GEN_OUTPUT_FILES_eus}"
)

add_custom_target(code_llm_generate_messages_eus
  DEPENDS ${ALL_GEN_OUTPUT_FILES_eus}
)
add_dependencies(code_llm_generate_messages code_llm_generate_messages_eus)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_eus _code_llm_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(code_llm_geneus)
add_dependencies(code_llm_geneus code_llm_generate_messages_eus)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS code_llm_generate_messages_eus)

### Section generating for lang: genlisp
### Generating Messages
_generate_msg_lisp(code_llm
  "/catkin_ws/src/code_llm/msg/Observations.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)
_generate_msg_lisp(code_llm
  "/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)

### Generating Services
_generate_srv_lisp(code_llm
  "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)
_generate_srv_lisp(code_llm
  "/catkin_ws/src/code_llm/srv/GetCharPoints.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg"
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)
_generate_srv_lisp(code_llm
  "/catkin_ws/src/code_llm/srv/ConnectEntities.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)
_generate_srv_lisp(code_llm
  "/catkin_ws/src/code_llm/srv/StartEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)
_generate_srv_lisp(code_llm
  "/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
)

### Generating Module File
_generate_module_lisp(code_llm
  ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
  "${ALL_GEN_OUTPUT_FILES_lisp}"
)

add_custom_target(code_llm_generate_messages_lisp
  DEPENDS ${ALL_GEN_OUTPUT_FILES_lisp}
)
add_dependencies(code_llm_generate_messages code_llm_generate_messages_lisp)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_lisp _code_llm_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(code_llm_genlisp)
add_dependencies(code_llm_genlisp code_llm_generate_messages_lisp)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS code_llm_generate_messages_lisp)

### Section generating for lang: gennodejs
### Generating Messages
_generate_msg_nodejs(code_llm
  "/catkin_ws/src/code_llm/msg/Observations.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)
_generate_msg_nodejs(code_llm
  "/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)

### Generating Services
_generate_srv_nodejs(code_llm
  "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)
_generate_srv_nodejs(code_llm
  "/catkin_ws/src/code_llm/srv/GetCharPoints.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg"
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)
_generate_srv_nodejs(code_llm
  "/catkin_ws/src/code_llm/srv/ConnectEntities.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)
_generate_srv_nodejs(code_llm
  "/catkin_ws/src/code_llm/srv/StartEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)
_generate_srv_nodejs(code_llm
  "/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
)

### Generating Module File
_generate_module_nodejs(code_llm
  ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
  "${ALL_GEN_OUTPUT_FILES_nodejs}"
)

add_custom_target(code_llm_generate_messages_nodejs
  DEPENDS ${ALL_GEN_OUTPUT_FILES_nodejs}
)
add_dependencies(code_llm_generate_messages code_llm_generate_messages_nodejs)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_nodejs _code_llm_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(code_llm_gennodejs)
add_dependencies(code_llm_gennodejs code_llm_generate_messages_nodejs)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS code_llm_generate_messages_nodejs)

### Section generating for lang: genpy
### Generating Messages
_generate_msg_py(code_llm
  "/catkin_ws/src/code_llm/msg/Observations.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)
_generate_msg_py(code_llm
  "/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)

### Generating Services
_generate_srv_py(code_llm
  "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Vector3.msg;/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Twist.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)
_generate_srv_py(code_llm
  "/catkin_ws/src/code_llm/srv/GetCharPoints.srv"
  "${MSG_I_FLAGS}"
  "/opt/ros/noetic/share/geometry_msgs/cmake/../msg/Point.msg"
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)
_generate_srv_py(code_llm
  "/catkin_ws/src/code_llm/srv/ConnectEntities.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)
_generate_srv_py(code_llm
  "/catkin_ws/src/code_llm/srv/StartEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)
_generate_srv_py(code_llm
  "/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
  "${MSG_I_FLAGS}"
  ""
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
)

### Generating Module File
_generate_module_py(code_llm
  ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
  "${ALL_GEN_OUTPUT_FILES_py}"
)

add_custom_target(code_llm_generate_messages_py
  DEPENDS ${ALL_GEN_OUTPUT_FILES_py}
)
add_dependencies(code_llm_generate_messages code_llm_generate_messages_py)

# add dependencies to all check dependencies targets
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/Observations.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/msg/ObjInfo.msg" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/GetCharPoints.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/ConnectEntities.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StartEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})
get_filename_component(_filename "/catkin_ws/src/code_llm/srv/StopEnvironment.srv" NAME_WE)
add_dependencies(code_llm_generate_messages_py _code_llm_generate_messages_check_deps_${_filename})

# target for backward compatibility
add_custom_target(code_llm_genpy)
add_dependencies(code_llm_genpy code_llm_generate_messages_py)

# register target for catkin_package(EXPORTED_TARGETS)
list(APPEND ${PROJECT_NAME}_EXPORTED_TARGETS code_llm_generate_messages_py)



if(gencpp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gencpp_INSTALL_DIR}/code_llm
    DESTINATION ${gencpp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_cpp)
  add_dependencies(code_llm_generate_messages_cpp std_msgs_generate_messages_cpp)
endif()
if(TARGET geometry_msgs_generate_messages_cpp)
  add_dependencies(code_llm_generate_messages_cpp geometry_msgs_generate_messages_cpp)
endif()

if(geneus_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${geneus_INSTALL_DIR}/code_llm
    DESTINATION ${geneus_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_eus)
  add_dependencies(code_llm_generate_messages_eus std_msgs_generate_messages_eus)
endif()
if(TARGET geometry_msgs_generate_messages_eus)
  add_dependencies(code_llm_generate_messages_eus geometry_msgs_generate_messages_eus)
endif()

if(genlisp_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genlisp_INSTALL_DIR}/code_llm
    DESTINATION ${genlisp_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_lisp)
  add_dependencies(code_llm_generate_messages_lisp std_msgs_generate_messages_lisp)
endif()
if(TARGET geometry_msgs_generate_messages_lisp)
  add_dependencies(code_llm_generate_messages_lisp geometry_msgs_generate_messages_lisp)
endif()

if(gennodejs_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm)
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${gennodejs_INSTALL_DIR}/code_llm
    DESTINATION ${gennodejs_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_nodejs)
  add_dependencies(code_llm_generate_messages_nodejs std_msgs_generate_messages_nodejs)
endif()
if(TARGET geometry_msgs_generate_messages_nodejs)
  add_dependencies(code_llm_generate_messages_nodejs geometry_msgs_generate_messages_nodejs)
endif()

if(genpy_INSTALL_DIR AND EXISTS ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm)
  install(CODE "execute_process(COMMAND \"/usr/bin/python3\" -m compileall \"${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm\")")
  # install generated code
  install(
    DIRECTORY ${CATKIN_DEVEL_PREFIX}/${genpy_INSTALL_DIR}/code_llm
    DESTINATION ${genpy_INSTALL_DIR}
  )
endif()
if(TARGET std_msgs_generate_messages_py)
  add_dependencies(code_llm_generate_messages_py std_msgs_generate_messages_py)
endif()
if(TARGET geometry_msgs_generate_messages_py)
  add_dependencies(code_llm_generate_messages_py geometry_msgs_generate_messages_py)
endif()
