# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/catkin_ws/src/code_llm/msg/Observations.msg;/catkin_ws/src/code_llm/msg/ObjInfo.msg"
services_str = "/catkin_ws/src/code_llm/srv/GetTargetPositions.srv;/catkin_ws/src/code_llm/srv/GetCharPoints.srv;/catkin_ws/src/code_llm/srv/ConnectEntities.srv;/catkin_ws/src/code_llm/srv/StartEnvironment.srv;/catkin_ws/src/code_llm/srv/StopEnvironment.srv"
pkg_name = "code_llm"
dependencies_str = "std_msgs;geometry_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "code_llm;/catkin_ws/src/code_llm/msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
