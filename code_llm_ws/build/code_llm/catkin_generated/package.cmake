set(_CATKIN_CURRENT_PACKAGE "code_llm")
set(code_llm_VERSION "0.0.0")
set(code_llm_MAINTAINER "derrick <<EMAIL>>")
set(code_llm_PACKAGE_FORMAT "2")
set(code_llm_BUILD_DEPENDS "rospy" "std_msgs" "message_generation" "geometry_msgs")
set(code_llm_BUILD_EXPORT_DEPENDS "rospy" "std_msgs" "geometry_msgs")
set(code_llm_BUILDTOOL_DEPENDS "catkin")
set(code_llm_BUILDTOOL_EXPORT_DEPENDS )
set(code_llm_EXEC_DEPENDS "rospy" "std_msgs" "message_runtime" "geometry_msgs")
set(code_llm_RUN_DEPENDS "rospy" "std_msgs" "message_runtime" "geometry_msgs")
set(code_llm_TEST_DEPENDS )
set(code_llm_DOC_DEPENDS )
set(code_llm_URL_WEBSITE "")
set(code_llm_URL_BUGTRACKER "")
set(code_llm_URL_REPOSITORY "")
set(code_llm_DEPRECATED "")