Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /catkin_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_adf49/fast && /usr/bin/make -f CMakeFiles/cmTC_adf49.dir/build.make CMakeFiles/cmTC_adf49.dir/build
make[1]: Entering directory '/catkin_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_adf49.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_adf49.dir/src.c.o   -c /catkin_ws/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_adf49
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_adf49.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_adf49.dir/src.c.o  -o cmTC_adf49 
/usr/bin/ld: CMakeFiles/cmTC_adf49.dir/src.c.o: in function `main':
src.c:(.text+0x48): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x50): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5c): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_adf49.dir/build.make:87: cmTC_adf49] Error 1
make[1]: Leaving directory '/catkin_ws/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_adf49/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /catkin_ws/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_ba3f4/fast && /usr/bin/make -f CMakeFiles/cmTC_ba3f4.dir/build.make CMakeFiles/cmTC_ba3f4.dir/build
make[1]: Entering directory '/catkin_ws/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_ba3f4.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_ba3f4.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_ba3f4
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ba3f4.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_ba3f4.dir/CheckFunctionExists.c.o  -o cmTC_ba3f4  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_ba3f4.dir/build.make:87: cmTC_ba3f4] Error 1
make[1]: Leaving directory '/catkin_ws/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_ba3f4/fast] Error 2



