#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/catkin_ws/devel:$CMAKE_PREFIX_PATH"
export LD_LIBRARY_PATH="/catkin_ws/devel/lib:$LD_LIBRARY_PATH"
export PKG_CONFIG_PATH="/catkin_ws/devel/lib/pkgconfig:$PKG_CONFIG_PATH"
export PWD='/catkin_ws/build'
export PYTHONPATH="/catkin_ws/devel/lib/python3/dist-packages:$PYTHONPATH"
export ROSLISP_PACKAGE_DIRECTORIES='/catkin_ws/devel/share/common-lisp'
export ROS_PACKAGE_PATH="/catkin_ws/src:$ROS_PACKAGE_PATH"