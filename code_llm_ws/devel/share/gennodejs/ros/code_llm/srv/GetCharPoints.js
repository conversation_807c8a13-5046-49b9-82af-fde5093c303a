// Auto-generated. Do not edit!

// (in-package code_llm.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;

//-----------------------------------------------------------

let geometry_msgs = _finder('geometry_msgs');

//-----------------------------------------------------------

class GetCharPointsRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.character = null;
    }
    else {
      if (initObj.hasOwnProperty('character')) {
        this.character = initObj.character
      }
      else {
        this.character = '';
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetCharPointsRequest
    // Serialize message field [character]
    bufferOffset = _serializer.string(obj.character, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetCharPointsRequest
    let len;
    let data = new GetCharPointsRequest(null);
    // Deserialize message field [character]
    data.character = _deserializer.string(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.character);
    return length + 4;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/GetCharPointsRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '955075ad51d7005d324d0bd26242a6db';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    string character
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetCharPointsRequest(null);
    if (msg.character !== undefined) {
      resolved.character = msg.character;
    }
    else {
      resolved.character = ''
    }

    return resolved;
    }
};

class GetCharPointsResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.points = null;
    }
    else {
      if (initObj.hasOwnProperty('points')) {
        this.points = initObj.points
      }
      else {
        this.points = [];
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetCharPointsResponse
    // Serialize message field [points]
    // Serialize the length for message field [points]
    bufferOffset = _serializer.uint32(obj.points.length, buffer, bufferOffset);
    obj.points.forEach((val) => {
      bufferOffset = geometry_msgs.msg.Point.serialize(val, buffer, bufferOffset);
    });
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetCharPointsResponse
    let len;
    let data = new GetCharPointsResponse(null);
    // Deserialize message field [points]
    // Deserialize array length for message field [points]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.points = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.points[i] = geometry_msgs.msg.Point.deserialize(buffer, bufferOffset)
    }
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += 24 * object.points.length;
    return length + 4;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/GetCharPointsResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '3fb3f9dacc279b964c4c8341122c34df';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    geometry_msgs/Point[] points
    
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetCharPointsResponse(null);
    if (msg.points !== undefined) {
      resolved.points = new Array(msg.points.length);
      for (let i = 0; i < resolved.points.length; ++i) {
        resolved.points[i] = geometry_msgs.msg.Point.Resolve(msg.points[i]);
      }
    }
    else {
      resolved.points = []
    }

    return resolved;
    }
};

module.exports = {
  Request: GetCharPointsRequest,
  Response: GetCharPointsResponse,
  md5sum() { return '424d0a1ec31ac62fb6e85eff0199a4bb'; },
  datatype() { return 'code_llm/GetCharPoints'; }
};
