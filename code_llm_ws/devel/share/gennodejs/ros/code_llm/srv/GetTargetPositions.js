// Auto-generated. Do not edit!

// (in-package code_llm.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;

//-----------------------------------------------------------

let ObjInfo = require('../msg/ObjInfo.js');

//-----------------------------------------------------------

class GetTargetPositionsRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
    }
    else {
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetTargetPositionsRequest
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetTargetPositionsRequest
    let len;
    let data = new GetTargetPositionsRequest(null);
    return data;
  }

  static getMessageSize(object) {
    return 0;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/GetTargetPositionsRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return 'd41d8cd98f00b204e9800998ecf8427e';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetTargetPositionsRequest(null);
    return resolved;
    }
};

class GetTargetPositionsResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.target_positions = null;
    }
    else {
      if (initObj.hasOwnProperty('target_positions')) {
        this.target_positions = initObj.target_positions
      }
      else {
        this.target_positions = [];
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type GetTargetPositionsResponse
    // Serialize message field [target_positions]
    // Serialize the length for message field [target_positions]
    bufferOffset = _serializer.uint32(obj.target_positions.length, buffer, bufferOffset);
    obj.target_positions.forEach((val) => {
      bufferOffset = ObjInfo.serialize(val, buffer, bufferOffset);
    });
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type GetTargetPositionsResponse
    let len;
    let data = new GetTargetPositionsResponse(null);
    // Deserialize message field [target_positions]
    // Deserialize array length for message field [target_positions]
    len = _deserializer.uint32(buffer, bufferOffset);
    data.target_positions = new Array(len);
    for (let i = 0; i < len; ++i) {
      data.target_positions[i] = ObjInfo.deserialize(buffer, bufferOffset)
    }
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    object.target_positions.forEach((val) => {
      length += ObjInfo.getMessageSize(val);
    });
    return length + 4;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/GetTargetPositionsResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '4d1569a822e71490fd292b69ce0fc339';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    ObjInfo[] target_positions
    
    
    ================================================================================
    MSG: code_llm/ObjInfo
    int32 id
    string type
    geometry_msgs/Point position
    geometry_msgs/Twist velocity
    geometry_msgs/Point target_position
    float32 radius
    string color
    
    ================================================================================
    MSG: geometry_msgs/Point
    # This contains the position of a point in free space
    float64 x
    float64 y
    float64 z
    
    ================================================================================
    MSG: geometry_msgs/Twist
    # This expresses velocity in free space broken into its linear and angular parts.
    Vector3  linear
    Vector3  angular
    
    ================================================================================
    MSG: geometry_msgs/Vector3
    # This represents a vector in free space. 
    # It is only meant to represent a direction. Therefore, it does not
    # make sense to apply a translation to it (e.g., when applying a 
    # generic rigid transformation to a Vector3, tf2 will only apply the
    # rotation). If you want your data to be translatable too, use the
    # geometry_msgs/Point message instead.
    
    float64 x
    float64 y
    float64 z
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new GetTargetPositionsResponse(null);
    if (msg.target_positions !== undefined) {
      resolved.target_positions = new Array(msg.target_positions.length);
      for (let i = 0; i < resolved.target_positions.length; ++i) {
        resolved.target_positions[i] = ObjInfo.Resolve(msg.target_positions[i]);
      }
    }
    else {
      resolved.target_positions = []
    }

    return resolved;
    }
};

module.exports = {
  Request: GetTargetPositionsRequest,
  Response: GetTargetPositionsResponse,
  md5sum() { return '4d1569a822e71490fd292b69ce0fc339'; },
  datatype() { return 'code_llm/GetTargetPositions'; }
};
