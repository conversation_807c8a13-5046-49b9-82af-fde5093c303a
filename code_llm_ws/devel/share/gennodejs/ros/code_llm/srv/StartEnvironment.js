// Auto-generated. Do not edit!

// (in-package code_llm.srv)


"use strict";

const _serializer = _ros_msg_utils.Serialize;
const _arraySerializer = _serializer.Array;
const _deserializer = _ros_msg_utils.Deserialize;
const _arrayDeserializer = _deserializer.Array;
const _finder = _ros_msg_utils.Find;
const _getByteLength = _ros_msg_utils.getByteLength;

//-----------------------------------------------------------


//-----------------------------------------------------------

class StartEnvironmentRequest {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.experiment_path = null;
    }
    else {
      if (initObj.hasOwnProperty('experiment_path')) {
        this.experiment_path = initObj.experiment_path
      }
      else {
        this.experiment_path = '';
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type StartEnvironmentRequest
    // Serialize message field [experiment_path]
    bufferOffset = _serializer.string(obj.experiment_path, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type StartEnvironmentRequest
    let len;
    let data = new StartEnvironmentRequest(null);
    // Deserialize message field [experiment_path]
    data.experiment_path = _deserializer.string(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.experiment_path);
    return length + 4;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/StartEnvironmentRequest';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '22169514ab4c983fbbbb1dd2d9401961';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    # StartEnvironment.srv
    string experiment_path
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new StartEnvironmentRequest(null);
    if (msg.experiment_path !== undefined) {
      resolved.experiment_path = msg.experiment_path;
    }
    else {
      resolved.experiment_path = ''
    }

    return resolved;
    }
};

class StartEnvironmentResponse {
  constructor(initObj={}) {
    if (initObj === null) {
      // initObj === null is a special case for deserialization where we don't initialize fields
      this.success = null;
      this.message = null;
    }
    else {
      if (initObj.hasOwnProperty('success')) {
        this.success = initObj.success
      }
      else {
        this.success = false;
      }
      if (initObj.hasOwnProperty('message')) {
        this.message = initObj.message
      }
      else {
        this.message = '';
      }
    }
  }

  static serialize(obj, buffer, bufferOffset) {
    // Serializes a message object of type StartEnvironmentResponse
    // Serialize message field [success]
    bufferOffset = _serializer.bool(obj.success, buffer, bufferOffset);
    // Serialize message field [message]
    bufferOffset = _serializer.string(obj.message, buffer, bufferOffset);
    return bufferOffset;
  }

  static deserialize(buffer, bufferOffset=[0]) {
    //deserializes a message object of type StartEnvironmentResponse
    let len;
    let data = new StartEnvironmentResponse(null);
    // Deserialize message field [success]
    data.success = _deserializer.bool(buffer, bufferOffset);
    // Deserialize message field [message]
    data.message = _deserializer.string(buffer, bufferOffset);
    return data;
  }

  static getMessageSize(object) {
    let length = 0;
    length += _getByteLength(object.message);
    return length + 5;
  }

  static datatype() {
    // Returns string type for a service object
    return 'code_llm/StartEnvironmentResponse';
  }

  static md5sum() {
    //Returns md5sum for a message object
    return '937c9679a518e3a18d831e57125ea522';
  }

  static messageDefinition() {
    // Returns full string definition for message
    return `
    bool success
    string message
    
    
    `;
  }

  static Resolve(msg) {
    // deep-construct a valid message object instance of whatever was passed in
    if (typeof msg !== 'object' || msg === null) {
      msg = {};
    }
    const resolved = new StartEnvironmentResponse(null);
    if (msg.success !== undefined) {
      resolved.success = msg.success;
    }
    else {
      resolved.success = false
    }

    if (msg.message !== undefined) {
      resolved.message = msg.message;
    }
    else {
      resolved.message = ''
    }

    return resolved;
    }
};

module.exports = {
  Request: StartEnvironmentRequest,
  Response: StartEnvironmentResponse,
  md5sum() { return '30d0efe8e416fa584926546cceee620d'; },
  datatype() { return 'code_llm/StartEnvironment'; }
};
