; Auto-generated. Do not edit!


(cl:in-package code_llm-srv)


;//! \htmlinclude GetCharPoints-request.msg.html

(cl:defclass <GetCharPoints-request> (roslisp-msg-protocol:ros-message)
  ((character
    :reader character
    :initarg :character
    :type cl:string
    :initform ""))
)

(cl:defclass GetCharPoints-request (<GetCharPoints-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetCharPoints-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetCharPoints-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<GetCharPoints-request> is deprecated: use code_llm-srv:GetCharPoints-request instead.")))

(cl:ensure-generic-function 'character-val :lambda-list '(m))
(cl:defmethod character-val ((m <GetCharPoints-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:character-val is deprecated.  Use code_llm-srv:character instead.")
  (character m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetCharPoints-request>) ostream)
  "Serializes a message object of type '<GetCharPoints-request>"
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'character))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'character))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetCharPoints-request>) istream)
  "Deserializes a message object of type '<GetCharPoints-request>"
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'character) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'character) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetCharPoints-request>)))
  "Returns string type for a service object of type '<GetCharPoints-request>"
  "code_llm/GetCharPointsRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetCharPoints-request)))
  "Returns string type for a service object of type 'GetCharPoints-request"
  "code_llm/GetCharPointsRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetCharPoints-request>)))
  "Returns md5sum for a message object of type '<GetCharPoints-request>"
  "424d0a1ec31ac62fb6e85eff0199a4bb")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetCharPoints-request)))
  "Returns md5sum for a message object of type 'GetCharPoints-request"
  "424d0a1ec31ac62fb6e85eff0199a4bb")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetCharPoints-request>)))
  "Returns full string definition for message of type '<GetCharPoints-request>"
  (cl:format cl:nil "string character~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetCharPoints-request)))
  "Returns full string definition for message of type 'GetCharPoints-request"
  (cl:format cl:nil "string character~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetCharPoints-request>))
  (cl:+ 0
     4 (cl:length (cl:slot-value msg 'character))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetCharPoints-request>))
  "Converts a ROS message object to a list"
  (cl:list 'GetCharPoints-request
    (cl:cons ':character (character msg))
))
;//! \htmlinclude GetCharPoints-response.msg.html

(cl:defclass <GetCharPoints-response> (roslisp-msg-protocol:ros-message)
  ((points
    :reader points
    :initarg :points
    :type (cl:vector geometry_msgs-msg:Point)
   :initform (cl:make-array 0 :element-type 'geometry_msgs-msg:Point :initial-element (cl:make-instance 'geometry_msgs-msg:Point))))
)

(cl:defclass GetCharPoints-response (<GetCharPoints-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetCharPoints-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetCharPoints-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<GetCharPoints-response> is deprecated: use code_llm-srv:GetCharPoints-response instead.")))

(cl:ensure-generic-function 'points-val :lambda-list '(m))
(cl:defmethod points-val ((m <GetCharPoints-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:points-val is deprecated.  Use code_llm-srv:points instead.")
  (points m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetCharPoints-response>) ostream)
  "Serializes a message object of type '<GetCharPoints-response>"
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'points))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'points))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetCharPoints-response>) istream)
  "Deserializes a message object of type '<GetCharPoints-response>"
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'points) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'points)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'geometry_msgs-msg:Point))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetCharPoints-response>)))
  "Returns string type for a service object of type '<GetCharPoints-response>"
  "code_llm/GetCharPointsResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetCharPoints-response)))
  "Returns string type for a service object of type 'GetCharPoints-response"
  "code_llm/GetCharPointsResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetCharPoints-response>)))
  "Returns md5sum for a message object of type '<GetCharPoints-response>"
  "424d0a1ec31ac62fb6e85eff0199a4bb")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetCharPoints-response)))
  "Returns md5sum for a message object of type 'GetCharPoints-response"
  "424d0a1ec31ac62fb6e85eff0199a4bb")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetCharPoints-response>)))
  "Returns full string definition for message of type '<GetCharPoints-response>"
  (cl:format cl:nil "geometry_msgs/Point[] points~%~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetCharPoints-response)))
  "Returns full string definition for message of type 'GetCharPoints-response"
  (cl:format cl:nil "geometry_msgs/Point[] points~%~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetCharPoints-response>))
  (cl:+ 0
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'points) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetCharPoints-response>))
  "Converts a ROS message object to a list"
  (cl:list 'GetCharPoints-response
    (cl:cons ':points (points msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'GetCharPoints)))
  'GetCharPoints-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'GetCharPoints)))
  'GetCharPoints-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetCharPoints)))
  "Returns string type for a service object of type '<GetCharPoints>"
  "code_llm/GetCharPoints")