(cl:defpackage code_llm-srv
  (:use )
  (:export
   "CONNECTENTITIES"
   "<CONNECTENTITIES-REQUEST>"
   "CONNECTENTITIES-REQUEST"
   "<CONNECTENTITIES-RESPONSE>"
   "CONNECTENTITIES-RESPONSE"
   "GETCHARPOINTS"
   "<GETCHARPOINTS-REQUEST>"
   "GETCHARPOINTS-REQUEST"
   "<GETCHARPOINTS-RESPONSE>"
   "GETCHARPOINTS-RESPONSE"
   "GETTARGETPOSITIONS"
   "<GETTARGETPOSITIONS-REQUEST>"
   "GETTARGETPOSITIONS-REQUEST"
   "<GETTARGETPOSITIONS-RESPONSE>"
   "GETTARGETPOSITIONS-RESPONSE"
   "STARTENVIRONMENT"
   "<STARTENVIRONMENT-REQUEST>"
   "STARTENVIRONMENT-REQUEST"
   "<STARTENVIRONMENT-RESPONSE>"
   "STARTENVIRONMENT-RESPONSE"
   "STOPENVIRONMENT"
   "<STOPENVIRONMENT-REQUEST>"
   "STOPENVIRONMENT-REQUEST"
   "<STOPENVIRONMENT-RESPONSE>"
   "STOPENVIRONMENT-RESPONSE"
  ))

