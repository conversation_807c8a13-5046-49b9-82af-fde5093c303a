; Auto-generated. Do not edit!


(cl:in-package code_llm-srv)


;//! \htmlinclude StopEnvironment-request.msg.html

(cl:defclass <StopEnvironment-request> (roslisp-msg-protocol:ros-message)
  ((file_name
    :reader file_name
    :initarg :file_name
    :type cl:string
    :initform ""))
)

(cl:defclass StopEnvironment-request (<StopEnvironment-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <StopEnvironment-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'StopEnvironment-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<StopEnvironment-request> is deprecated: use code_llm-srv:StopEnvironment-request instead.")))

(cl:ensure-generic-function 'file_name-val :lambda-list '(m))
(cl:defmethod file_name-val ((m <StopEnvironment-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:file_name-val is deprecated.  Use code_llm-srv:file_name instead.")
  (file_name m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <StopEnvironment-request>) ostream)
  "Serializes a message object of type '<StopEnvironment-request>"
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'file_name))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'file_name))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <StopEnvironment-request>) istream)
  "Deserializes a message object of type '<StopEnvironment-request>"
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'file_name) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'file_name) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<StopEnvironment-request>)))
  "Returns string type for a service object of type '<StopEnvironment-request>"
  "code_llm/StopEnvironmentRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StopEnvironment-request)))
  "Returns string type for a service object of type 'StopEnvironment-request"
  "code_llm/StopEnvironmentRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<StopEnvironment-request>)))
  "Returns md5sum for a message object of type '<StopEnvironment-request>"
  "e4aac7123a01c95d7b97c868c71b4525")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'StopEnvironment-request)))
  "Returns md5sum for a message object of type 'StopEnvironment-request"
  "e4aac7123a01c95d7b97c868c71b4525")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<StopEnvironment-request>)))
  "Returns full string definition for message of type '<StopEnvironment-request>"
  (cl:format cl:nil "# StopEnvironment.srv~%string file_name~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'StopEnvironment-request)))
  "Returns full string definition for message of type 'StopEnvironment-request"
  (cl:format cl:nil "# StopEnvironment.srv~%string file_name~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <StopEnvironment-request>))
  (cl:+ 0
     4 (cl:length (cl:slot-value msg 'file_name))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <StopEnvironment-request>))
  "Converts a ROS message object to a list"
  (cl:list 'StopEnvironment-request
    (cl:cons ':file_name (file_name msg))
))
;//! \htmlinclude StopEnvironment-response.msg.html

(cl:defclass <StopEnvironment-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil)
   (message
    :reader message
    :initarg :message
    :type cl:string
    :initform ""))
)

(cl:defclass StopEnvironment-response (<StopEnvironment-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <StopEnvironment-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'StopEnvironment-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<StopEnvironment-response> is deprecated: use code_llm-srv:StopEnvironment-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <StopEnvironment-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:success-val is deprecated.  Use code_llm-srv:success instead.")
  (success m))

(cl:ensure-generic-function 'message-val :lambda-list '(m))
(cl:defmethod message-val ((m <StopEnvironment-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:message-val is deprecated.  Use code_llm-srv:message instead.")
  (message m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <StopEnvironment-response>) ostream)
  "Serializes a message object of type '<StopEnvironment-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'message))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'message))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <StopEnvironment-response>) istream)
  "Deserializes a message object of type '<StopEnvironment-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'message) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'message) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<StopEnvironment-response>)))
  "Returns string type for a service object of type '<StopEnvironment-response>"
  "code_llm/StopEnvironmentResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StopEnvironment-response)))
  "Returns string type for a service object of type 'StopEnvironment-response"
  "code_llm/StopEnvironmentResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<StopEnvironment-response>)))
  "Returns md5sum for a message object of type '<StopEnvironment-response>"
  "e4aac7123a01c95d7b97c868c71b4525")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'StopEnvironment-response)))
  "Returns md5sum for a message object of type 'StopEnvironment-response"
  "e4aac7123a01c95d7b97c868c71b4525")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<StopEnvironment-response>)))
  "Returns full string definition for message of type '<StopEnvironment-response>"
  (cl:format cl:nil "bool success~%string message~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'StopEnvironment-response)))
  "Returns full string definition for message of type 'StopEnvironment-response"
  (cl:format cl:nil "bool success~%string message~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <StopEnvironment-response>))
  (cl:+ 0
     1
     4 (cl:length (cl:slot-value msg 'message))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <StopEnvironment-response>))
  "Converts a ROS message object to a list"
  (cl:list 'StopEnvironment-response
    (cl:cons ':success (success msg))
    (cl:cons ':message (message msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'StopEnvironment)))
  'StopEnvironment-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'StopEnvironment)))
  'StopEnvironment-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StopEnvironment)))
  "Returns string type for a service object of type '<StopEnvironment>"
  "code_llm/StopEnvironment")