; Auto-generated. Do not edit!


(cl:in-package code_llm-srv)


;//! \htmlinclude StartEnvironment-request.msg.html

(cl:defclass <StartEnvironment-request> (roslisp-msg-protocol:ros-message)
  ((experiment_path
    :reader experiment_path
    :initarg :experiment_path
    :type cl:string
    :initform ""))
)

(cl:defclass StartEnvironment-request (<StartEnvironment-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <StartEnvironment-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'StartEnvironment-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<StartEnvironment-request> is deprecated: use code_llm-srv:StartEnvironment-request instead.")))

(cl:ensure-generic-function 'experiment_path-val :lambda-list '(m))
(cl:defmethod experiment_path-val ((m <StartEnvironment-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:experiment_path-val is deprecated.  Use code_llm-srv:experiment_path instead.")
  (experiment_path m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <StartEnvironment-request>) ostream)
  "Serializes a message object of type '<StartEnvironment-request>"
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'experiment_path))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'experiment_path))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <StartEnvironment-request>) istream)
  "Deserializes a message object of type '<StartEnvironment-request>"
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'experiment_path) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'experiment_path) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<StartEnvironment-request>)))
  "Returns string type for a service object of type '<StartEnvironment-request>"
  "code_llm/StartEnvironmentRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StartEnvironment-request)))
  "Returns string type for a service object of type 'StartEnvironment-request"
  "code_llm/StartEnvironmentRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<StartEnvironment-request>)))
  "Returns md5sum for a message object of type '<StartEnvironment-request>"
  "30d0efe8e416fa584926546cceee620d")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'StartEnvironment-request)))
  "Returns md5sum for a message object of type 'StartEnvironment-request"
  "30d0efe8e416fa584926546cceee620d")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<StartEnvironment-request>)))
  "Returns full string definition for message of type '<StartEnvironment-request>"
  (cl:format cl:nil "# StartEnvironment.srv~%string experiment_path~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'StartEnvironment-request)))
  "Returns full string definition for message of type 'StartEnvironment-request"
  (cl:format cl:nil "# StartEnvironment.srv~%string experiment_path~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <StartEnvironment-request>))
  (cl:+ 0
     4 (cl:length (cl:slot-value msg 'experiment_path))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <StartEnvironment-request>))
  "Converts a ROS message object to a list"
  (cl:list 'StartEnvironment-request
    (cl:cons ':experiment_path (experiment_path msg))
))
;//! \htmlinclude StartEnvironment-response.msg.html

(cl:defclass <StartEnvironment-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil)
   (message
    :reader message
    :initarg :message
    :type cl:string
    :initform ""))
)

(cl:defclass StartEnvironment-response (<StartEnvironment-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <StartEnvironment-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'StartEnvironment-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<StartEnvironment-response> is deprecated: use code_llm-srv:StartEnvironment-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <StartEnvironment-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:success-val is deprecated.  Use code_llm-srv:success instead.")
  (success m))

(cl:ensure-generic-function 'message-val :lambda-list '(m))
(cl:defmethod message-val ((m <StartEnvironment-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:message-val is deprecated.  Use code_llm-srv:message instead.")
  (message m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <StartEnvironment-response>) ostream)
  "Serializes a message object of type '<StartEnvironment-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'message))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'message))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <StartEnvironment-response>) istream)
  "Deserializes a message object of type '<StartEnvironment-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'message) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'message) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<StartEnvironment-response>)))
  "Returns string type for a service object of type '<StartEnvironment-response>"
  "code_llm/StartEnvironmentResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StartEnvironment-response)))
  "Returns string type for a service object of type 'StartEnvironment-response"
  "code_llm/StartEnvironmentResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<StartEnvironment-response>)))
  "Returns md5sum for a message object of type '<StartEnvironment-response>"
  "30d0efe8e416fa584926546cceee620d")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'StartEnvironment-response)))
  "Returns md5sum for a message object of type 'StartEnvironment-response"
  "30d0efe8e416fa584926546cceee620d")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<StartEnvironment-response>)))
  "Returns full string definition for message of type '<StartEnvironment-response>"
  (cl:format cl:nil "bool success~%string message~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'StartEnvironment-response)))
  "Returns full string definition for message of type 'StartEnvironment-response"
  (cl:format cl:nil "bool success~%string message~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <StartEnvironment-response>))
  (cl:+ 0
     1
     4 (cl:length (cl:slot-value msg 'message))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <StartEnvironment-response>))
  "Converts a ROS message object to a list"
  (cl:list 'StartEnvironment-response
    (cl:cons ':success (success msg))
    (cl:cons ':message (message msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'StartEnvironment)))
  'StartEnvironment-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'StartEnvironment)))
  'StartEnvironment-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'StartEnvironment)))
  "Returns string type for a service object of type '<StartEnvironment>"
  "code_llm/StartEnvironment")