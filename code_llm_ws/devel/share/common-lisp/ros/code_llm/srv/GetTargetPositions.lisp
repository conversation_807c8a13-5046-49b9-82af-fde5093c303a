; Auto-generated. Do not edit!


(cl:in-package code_llm-srv)


;//! \htmlinclude GetTargetPositions-request.msg.html

(cl:defclass <GetTargetPositions-request> (roslisp-msg-protocol:ros-message)
  ()
)

(cl:defclass GetTargetPositions-request (<GetTargetPositions-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetTargetPositions-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetTargetPositions-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<GetTargetPositions-request> is deprecated: use code_llm-srv:GetTargetPositions-request instead.")))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetTargetPositions-request>) ostream)
  "Serializes a message object of type '<GetTargetPositions-request>"
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetTargetPositions-request>) istream)
  "Deserializes a message object of type '<GetTargetPositions-request>"
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetTargetPositions-request>)))
  "Returns string type for a service object of type '<GetTargetPositions-request>"
  "code_llm/GetTargetPositionsRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetTargetPositions-request)))
  "Returns string type for a service object of type 'GetTargetPositions-request"
  "code_llm/GetTargetPositionsRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetTargetPositions-request>)))
  "Returns md5sum for a message object of type '<GetTargetPositions-request>"
  "4d1569a822e71490fd292b69ce0fc339")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetTargetPositions-request)))
  "Returns md5sum for a message object of type 'GetTargetPositions-request"
  "4d1569a822e71490fd292b69ce0fc339")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetTargetPositions-request>)))
  "Returns full string definition for message of type '<GetTargetPositions-request>"
  (cl:format cl:nil "~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetTargetPositions-request)))
  "Returns full string definition for message of type 'GetTargetPositions-request"
  (cl:format cl:nil "~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetTargetPositions-request>))
  (cl:+ 0
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetTargetPositions-request>))
  "Converts a ROS message object to a list"
  (cl:list 'GetTargetPositions-request
))
;//! \htmlinclude GetTargetPositions-response.msg.html

(cl:defclass <GetTargetPositions-response> (roslisp-msg-protocol:ros-message)
  ((target_positions
    :reader target_positions
    :initarg :target_positions
    :type (cl:vector code_llm-msg:ObjInfo)
   :initform (cl:make-array 0 :element-type 'code_llm-msg:ObjInfo :initial-element (cl:make-instance 'code_llm-msg:ObjInfo))))
)

(cl:defclass GetTargetPositions-response (<GetTargetPositions-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <GetTargetPositions-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'GetTargetPositions-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<GetTargetPositions-response> is deprecated: use code_llm-srv:GetTargetPositions-response instead.")))

(cl:ensure-generic-function 'target_positions-val :lambda-list '(m))
(cl:defmethod target_positions-val ((m <GetTargetPositions-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:target_positions-val is deprecated.  Use code_llm-srv:target_positions instead.")
  (target_positions m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <GetTargetPositions-response>) ostream)
  "Serializes a message object of type '<GetTargetPositions-response>"
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'target_positions))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'target_positions))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <GetTargetPositions-response>) istream)
  "Deserializes a message object of type '<GetTargetPositions-response>"
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'target_positions) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'target_positions)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'code_llm-msg:ObjInfo))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<GetTargetPositions-response>)))
  "Returns string type for a service object of type '<GetTargetPositions-response>"
  "code_llm/GetTargetPositionsResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetTargetPositions-response)))
  "Returns string type for a service object of type 'GetTargetPositions-response"
  "code_llm/GetTargetPositionsResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<GetTargetPositions-response>)))
  "Returns md5sum for a message object of type '<GetTargetPositions-response>"
  "4d1569a822e71490fd292b69ce0fc339")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'GetTargetPositions-response)))
  "Returns md5sum for a message object of type 'GetTargetPositions-response"
  "4d1569a822e71490fd292b69ce0fc339")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<GetTargetPositions-response>)))
  "Returns full string definition for message of type '<GetTargetPositions-response>"
  (cl:format cl:nil "ObjInfo[] target_positions~%~%~%================================================================================~%MSG: code_llm/ObjInfo~%int32 id~%string type~%geometry_msgs/Point position~%geometry_msgs/Twist velocity~%geometry_msgs/Point target_position~%float32 radius~%string color~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Twist~%# This expresses velocity in free space broken into its linear and angular parts.~%Vector3  linear~%Vector3  angular~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'GetTargetPositions-response)))
  "Returns full string definition for message of type 'GetTargetPositions-response"
  (cl:format cl:nil "ObjInfo[] target_positions~%~%~%================================================================================~%MSG: code_llm/ObjInfo~%int32 id~%string type~%geometry_msgs/Point position~%geometry_msgs/Twist velocity~%geometry_msgs/Point target_position~%float32 radius~%string color~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Twist~%# This expresses velocity in free space broken into its linear and angular parts.~%Vector3  linear~%Vector3  angular~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <GetTargetPositions-response>))
  (cl:+ 0
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'target_positions) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <GetTargetPositions-response>))
  "Converts a ROS message object to a list"
  (cl:list 'GetTargetPositions-response
    (cl:cons ':target_positions (target_positions msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'GetTargetPositions)))
  'GetTargetPositions-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'GetTargetPositions)))
  'GetTargetPositions-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'GetTargetPositions)))
  "Returns string type for a service object of type '<GetTargetPositions>"
  "code_llm/GetTargetPositions")