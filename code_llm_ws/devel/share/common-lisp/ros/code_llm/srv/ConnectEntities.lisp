; Auto-generated. Do not edit!


(cl:in-package code_llm-srv)


;//! \htmlinclude ConnectEntities-request.msg.html

(cl:defclass <ConnectEntities-request> (roslisp-msg-protocol:ros-message)
  ((self_id
    :reader self_id
    :initarg :self_id
    :type cl:integer
    :initform 0)
   (target_id
    :reader target_id
    :initarg :target_id
    :type cl:integer
    :initform 0))
)

(cl:defclass ConnectEntities-request (<ConnectEntities-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <ConnectEntities-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'ConnectEntities-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<ConnectEntities-request> is deprecated: use code_llm-srv:ConnectEntities-request instead.")))

(cl:ensure-generic-function 'self_id-val :lambda-list '(m))
(cl:defmethod self_id-val ((m <ConnectEntities-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:self_id-val is deprecated.  Use code_llm-srv:self_id instead.")
  (self_id m))

(cl:ensure-generic-function 'target_id-val :lambda-list '(m))
(cl:defmethod target_id-val ((m <ConnectEntities-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:target_id-val is deprecated.  Use code_llm-srv:target_id instead.")
  (target_id m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <ConnectEntities-request>) ostream)
  "Serializes a message object of type '<ConnectEntities-request>"
  (cl:let* ((signed (cl:slot-value msg 'self_id)) (unsigned (cl:if (cl:< signed 0) (cl:+ signed 4294967296) signed)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) unsigned) ostream)
    )
  (cl:let* ((signed (cl:slot-value msg 'target_id)) (unsigned (cl:if (cl:< signed 0) (cl:+ signed 4294967296) signed)))
    (cl:write-byte (cl:ldb (cl:byte 8 0) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) unsigned) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) unsigned) ostream)
    )
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <ConnectEntities-request>) istream)
  "Deserializes a message object of type '<ConnectEntities-request>"
    (cl:let ((unsigned 0))
      (cl:setf (cl:ldb (cl:byte 8 0) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) unsigned) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'self_id) (cl:if (cl:< unsigned 2147483648) unsigned (cl:- unsigned 4294967296))))
    (cl:let ((unsigned 0))
      (cl:setf (cl:ldb (cl:byte 8 0) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) unsigned) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) unsigned) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'target_id) (cl:if (cl:< unsigned 2147483648) unsigned (cl:- unsigned 4294967296))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<ConnectEntities-request>)))
  "Returns string type for a service object of type '<ConnectEntities-request>"
  "code_llm/ConnectEntitiesRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'ConnectEntities-request)))
  "Returns string type for a service object of type 'ConnectEntities-request"
  "code_llm/ConnectEntitiesRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<ConnectEntities-request>)))
  "Returns md5sum for a message object of type '<ConnectEntities-request>"
  "667ef60adfe708beaa2fc0c1762188fa")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'ConnectEntities-request)))
  "Returns md5sum for a message object of type 'ConnectEntities-request"
  "667ef60adfe708beaa2fc0c1762188fa")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<ConnectEntities-request>)))
  "Returns full string definition for message of type '<ConnectEntities-request>"
  (cl:format cl:nil "int32 self_id~%int32 target_id~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'ConnectEntities-request)))
  "Returns full string definition for message of type 'ConnectEntities-request"
  (cl:format cl:nil "int32 self_id~%int32 target_id~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <ConnectEntities-request>))
  (cl:+ 0
     4
     4
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <ConnectEntities-request>))
  "Converts a ROS message object to a list"
  (cl:list 'ConnectEntities-request
    (cl:cons ':self_id (self_id msg))
    (cl:cons ':target_id (target_id msg))
))
;//! \htmlinclude ConnectEntities-response.msg.html

(cl:defclass <ConnectEntities-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil))
)

(cl:defclass ConnectEntities-response (<ConnectEntities-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <ConnectEntities-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'ConnectEntities-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-srv:<ConnectEntities-response> is deprecated: use code_llm-srv:ConnectEntities-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <ConnectEntities-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-srv:success-val is deprecated.  Use code_llm-srv:success instead.")
  (success m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <ConnectEntities-response>) ostream)
  "Serializes a message object of type '<ConnectEntities-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <ConnectEntities-response>) istream)
  "Deserializes a message object of type '<ConnectEntities-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<ConnectEntities-response>)))
  "Returns string type for a service object of type '<ConnectEntities-response>"
  "code_llm/ConnectEntitiesResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'ConnectEntities-response)))
  "Returns string type for a service object of type 'ConnectEntities-response"
  "code_llm/ConnectEntitiesResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<ConnectEntities-response>)))
  "Returns md5sum for a message object of type '<ConnectEntities-response>"
  "667ef60adfe708beaa2fc0c1762188fa")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'ConnectEntities-response)))
  "Returns md5sum for a message object of type 'ConnectEntities-response"
  "667ef60adfe708beaa2fc0c1762188fa")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<ConnectEntities-response>)))
  "Returns full string definition for message of type '<ConnectEntities-response>"
  (cl:format cl:nil "bool success~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'ConnectEntities-response)))
  "Returns full string definition for message of type 'ConnectEntities-response"
  (cl:format cl:nil "bool success~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <ConnectEntities-response>))
  (cl:+ 0
     1
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <ConnectEntities-response>))
  "Converts a ROS message object to a list"
  (cl:list 'ConnectEntities-response
    (cl:cons ':success (success msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'ConnectEntities)))
  'ConnectEntities-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'ConnectEntities)))
  'ConnectEntities-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'ConnectEntities)))
  "Returns string type for a service object of type '<ConnectEntities>"
  "code_llm/ConnectEntities")