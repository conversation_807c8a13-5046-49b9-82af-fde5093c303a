
(cl:in-package :asdf)

(defsystem "code_llm-srv"
  :depends-on (:roslisp-msg-protocol :roslisp-utils :code_llm-msg
               :geometry_msgs-msg
)
  :components ((:file "_package")
    (:file "ConnectEntities" :depends-on ("_package_ConnectEntities"))
    (:file "_package_ConnectEntities" :depends-on ("_package"))
    (:file "GetCharPoints" :depends-on ("_package_GetCharPoints"))
    (:file "_package_GetCharPoints" :depends-on ("_package"))
    (:file "GetTargetPositions" :depends-on ("_package_GetTargetPositions"))
    (:file "_package_GetTargetPositions" :depends-on ("_package"))
    (:file "StartEnvironment" :depends-on ("_package_StartEnvironment"))
    (:file "_package_StartEnvironment" :depends-on ("_package"))
    (:file "StopEnvironment" :depends-on ("_package_StopEnvironment"))
    (:file "_package_StopEnvironment" :depends-on ("_package"))
  ))