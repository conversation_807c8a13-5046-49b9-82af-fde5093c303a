; Auto-generated. Do not edit!


(cl:in-package code_llm-msg)


;//! \htmlinclude Observations.msg.html

(cl:defclass <Observations> (roslisp-msg-protocol:ros-message)
  ((observations
    :reader observations
    :initarg :observations
    :type (cl:vector code_llm-msg:ObjInfo)
   :initform (cl:make-array 0 :element-type 'code_llm-msg:ObjInfo :initial-element (cl:make-instance 'code_llm-msg:ObjInfo))))
)

(cl:defclass Observations (<Observations>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <Observations>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'Observations)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name code_llm-msg:<Observations> is deprecated: use code_llm-msg:Observations instead.")))

(cl:ensure-generic-function 'observations-val :lambda-list '(m))
(cl:defmethod observations-val ((m <Observations>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader code_llm-msg:observations-val is deprecated.  Use code_llm-msg:observations instead.")
  (observations m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <Observations>) ostream)
  "Serializes a message object of type '<Observations>"
  (cl:let ((__ros_arr_len (cl:length (cl:slot-value msg 'observations))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_arr_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_arr_len) ostream))
  (cl:map cl:nil #'(cl:lambda (ele) (roslisp-msg-protocol:serialize ele ostream))
   (cl:slot-value msg 'observations))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <Observations>) istream)
  "Deserializes a message object of type '<Observations>"
  (cl:let ((__ros_arr_len 0))
    (cl:setf (cl:ldb (cl:byte 8 0) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 8) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 16) __ros_arr_len) (cl:read-byte istream))
    (cl:setf (cl:ldb (cl:byte 8 24) __ros_arr_len) (cl:read-byte istream))
  (cl:setf (cl:slot-value msg 'observations) (cl:make-array __ros_arr_len))
  (cl:let ((vals (cl:slot-value msg 'observations)))
    (cl:dotimes (i __ros_arr_len)
    (cl:setf (cl:aref vals i) (cl:make-instance 'code_llm-msg:ObjInfo))
  (roslisp-msg-protocol:deserialize (cl:aref vals i) istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<Observations>)))
  "Returns string type for a message object of type '<Observations>"
  "code_llm/Observations")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'Observations)))
  "Returns string type for a message object of type 'Observations"
  "code_llm/Observations")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<Observations>)))
  "Returns md5sum for a message object of type '<Observations>"
  "2c2d592d2da77cd414a280b0f81875a8")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'Observations)))
  "Returns md5sum for a message object of type 'Observations"
  "2c2d592d2da77cd414a280b0f81875a8")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<Observations>)))
  "Returns full string definition for message of type '<Observations>"
  (cl:format cl:nil "# 存放单个机器人所有的感知信息~%~%ObjInfo[] observations~%~%================================================================================~%MSG: code_llm/ObjInfo~%int32 id~%string type~%geometry_msgs/Point position~%geometry_msgs/Twist velocity~%geometry_msgs/Point target_position~%float32 radius~%string color~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Twist~%# This expresses velocity in free space broken into its linear and angular parts.~%Vector3  linear~%Vector3  angular~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'Observations)))
  "Returns full string definition for message of type 'Observations"
  (cl:format cl:nil "# 存放单个机器人所有的感知信息~%~%ObjInfo[] observations~%~%================================================================================~%MSG: code_llm/ObjInfo~%int32 id~%string type~%geometry_msgs/Point position~%geometry_msgs/Twist velocity~%geometry_msgs/Point target_position~%float32 radius~%string color~%~%================================================================================~%MSG: geometry_msgs/Point~%# This contains the position of a point in free space~%float64 x~%float64 y~%float64 z~%~%================================================================================~%MSG: geometry_msgs/Twist~%# This expresses velocity in free space broken into its linear and angular parts.~%Vector3  linear~%Vector3  angular~%~%================================================================================~%MSG: geometry_msgs/Vector3~%# This represents a vector in free space. ~%# It is only meant to represent a direction. Therefore, it does not~%# make sense to apply a translation to it (e.g., when applying a ~%# generic rigid transformation to a Vector3, tf2 will only apply the~%# rotation). If you want your data to be translatable too, use the~%# geometry_msgs/Point message instead.~%~%float64 x~%float64 y~%float64 z~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <Observations>))
  (cl:+ 0
     4 (cl:reduce #'cl:+ (cl:slot-value msg 'observations) :key #'(cl:lambda (ele) (cl:declare (cl:ignorable ele)) (cl:+ (roslisp-msg-protocol:serialization-length ele))))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <Observations>))
  "Converts a ROS message object to a list"
  (cl:list 'Observations
    (cl:cons ':observations (observations msg))
))
