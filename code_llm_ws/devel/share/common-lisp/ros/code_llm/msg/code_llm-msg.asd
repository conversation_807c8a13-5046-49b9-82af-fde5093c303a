
(cl:in-package :asdf)

(defsystem "code_llm-msg"
  :depends-on (:roslisp-msg-protocol :roslisp-utils :geometry_msgs-msg
)
  :components ((:file "_package")
    (:file "ObjInfo" :depends-on ("_package_ObjInfo"))
    (:file "_package_ObjInfo" :depends-on ("_package"))
    (:file "Observations" :depends-on ("_package_Observations"))
    (:file "_package_Observations" :depends-on ("_package"))
    (:file "RobotVelocities" :depends-on ("_package_RobotVelocities"))
    (:file "_package_RobotVelocities" :depends-on ("_package"))
    (:file "RobotVelocity" :depends-on ("_package_RobotVelocity"))
    (:file "_package_RobotVelocity" :depends-on ("_package"))
  ))