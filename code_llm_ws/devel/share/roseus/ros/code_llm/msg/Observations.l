;; Auto-generated. Do not edit!


(when (boundp 'code_llm::Observations)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'Observations (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::OBSERVATIONS")
  (make-package "CODE_LLM::OBSERVATIONS"))

(in-package "ROS")
;;//! \htmlinclude Observations.msg.html


(defclass code_llm::Observations
  :super ros::object
  :slots (_observations ))

(defmethod code_llm::Observations
  (:init
   (&key
    ((:observations __observations) ())
    )
   (send-super :init)
   (setq _observations __observations)
   self)
  (:observations
   (&rest __observations)
   (if (keywordp (car __observations))
       (send* _observations __observations)
     (progn
       (if __observations (setq _observations (car __observations)))
       _observations)))
  (:serialization-length
   ()
   (+
    ;; code_llm/ObjInfo[] _observations
    (apply #'+ (send-all _observations :serialization-length)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; code_llm/ObjInfo[] _observations
     (write-long (length _observations) s)
     (dolist (elem _observations)
       (send elem :serialize s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; code_llm/ObjInfo[] _observations
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _observations (let (r) (dotimes (i n) (push (instance code_llm::ObjInfo :init) r)) r))
     (dolist (elem- _observations)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;;
   self)
  )

(setf (get code_llm::Observations :md5sum-) "2c2d592d2da77cd414a280b0f81875a8")
(setf (get code_llm::Observations :datatype-) "code_llm/Observations")
(setf (get code_llm::Observations :definition-)
      "# 存放单个机器人所有的感知信息

ObjInfo[] observations

================================================================================
MSG: code_llm/ObjInfo
int32 id
string type
geometry_msgs/Point position
geometry_msgs/Twist velocity
geometry_msgs/Point target_position
float32 radius
string color

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Twist
# This expresses velocity in free space broken into its linear and angular parts.
Vector3  linear
Vector3  angular

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
float64 z
")



(provide :code_llm/Observations "2c2d592d2da77cd414a280b0f81875a8")


