;; Auto-generated. Do not edit!


(when (boundp 'code_llm::StartEnvironment)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'StartEnvironment (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::STARTENVIRONMENT")
  (make-package "CODE_LLM::STARTENVIRONMENT"))
(unless (find-package "CODE_LLM::STARTENVIRONMENTREQUEST")
  (make-package "CODE_LLM::STARTENVIRONMENTREQUEST"))
(unless (find-package "CODE_LLM::STARTENVIRONMENTRESPONSE")
  (make-package "CODE_LLM::STARTENVIRONMENTRESPONSE"))

(in-package "ROS")





(defclass code_llm::StartEnvironmentRequest
  :super ros::object
  :slots (_experiment_path ))

(defmethod code_llm::StartEnvironmentRequest
  (:init
   (&key
    ((:experiment_path __experiment_path) "")
    )
   (send-super :init)
   (setq _experiment_path (string __experiment_path))
   self)
  (:experiment_path
   (&optional __experiment_path)
   (if __experiment_path (setq _experiment_path __experiment_path)) _experiment_path)
  (:serialization-length
   ()
   (+
    ;; string _experiment_path
    4 (length _experiment_path)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _experiment_path
       (write-long (length _experiment_path) s) (princ _experiment_path s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _experiment_path
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _experiment_path (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass code_llm::StartEnvironmentResponse
  :super ros::object
  :slots (_success _message ))

(defmethod code_llm::StartEnvironmentResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass code_llm::StartEnvironment
  :super ros::object
  :slots ())

(setf (get code_llm::StartEnvironment :md5sum-) "30d0efe8e416fa584926546cceee620d")
(setf (get code_llm::StartEnvironment :datatype-) "code_llm/StartEnvironment")
(setf (get code_llm::StartEnvironment :request) code_llm::StartEnvironmentRequest)
(setf (get code_llm::StartEnvironment :response) code_llm::StartEnvironmentResponse)

(defmethod code_llm::StartEnvironmentRequest
  (:response () (instance code_llm::StartEnvironmentResponse :init)))

(setf (get code_llm::StartEnvironmentRequest :md5sum-) "30d0efe8e416fa584926546cceee620d")
(setf (get code_llm::StartEnvironmentRequest :datatype-) "code_llm/StartEnvironmentRequest")
(setf (get code_llm::StartEnvironmentRequest :definition-)
      "# StartEnvironment.srv
string experiment_path
---
bool success
string message

")

(setf (get code_llm::StartEnvironmentResponse :md5sum-) "30d0efe8e416fa584926546cceee620d")
(setf (get code_llm::StartEnvironmentResponse :datatype-) "code_llm/StartEnvironmentResponse")
(setf (get code_llm::StartEnvironmentResponse :definition-)
      "# StartEnvironment.srv
string experiment_path
---
bool success
string message

")



(provide :code_llm/StartEnvironment "30d0efe8e416fa584926546cceee620d")


