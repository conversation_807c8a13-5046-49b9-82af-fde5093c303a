;; Auto-generated. Do not edit!


(when (boundp 'code_llm::GetTargetPositions)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'GetTargetPositions (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::GETTARGETPOSITIONS")
  (make-package "CODE_LLM::GETTARGETPOSITIONS"))
(unless (find-package "CODE_LLM::GETTARGETPOSITIONSREQUEST")
  (make-package "CODE_LLM::GETTARGETPOSITIONSREQUEST"))
(unless (find-package "CODE_LLM::GETTARGETPOSITIONSRESPONSE")
  (make-package "CODE_LLM::GETTARGETPOSITIONSRESPONSE"))

(in-package "ROS")





(defclass code_llm::GetTargetPositionsRequest
  :super ros::object
  :slots ())

(defmethod code_llm::GetTargetPositionsRequest
  (:init
   (&key
    )
   (send-super :init)
   self)
  (:serialization-length
   ()
   (+
    0
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;;
   self)
  )

(defclass code_llm::GetTargetPositionsResponse
  :super ros::object
  :slots (_target_positions ))

(defmethod code_llm::GetTargetPositionsResponse
  (:init
   (&key
    ((:target_positions __target_positions) ())
    )
   (send-super :init)
   (setq _target_positions __target_positions)
   self)
  (:target_positions
   (&rest __target_positions)
   (if (keywordp (car __target_positions))
       (send* _target_positions __target_positions)
     (progn
       (if __target_positions (setq _target_positions (car __target_positions)))
       _target_positions)))
  (:serialization-length
   ()
   (+
    ;; code_llm/ObjInfo[] _target_positions
    (apply #'+ (send-all _target_positions :serialization-length)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; code_llm/ObjInfo[] _target_positions
     (write-long (length _target_positions) s)
     (dolist (elem _target_positions)
       (send elem :serialize s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; code_llm/ObjInfo[] _target_positions
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _target_positions (let (r) (dotimes (i n) (push (instance code_llm::ObjInfo :init) r)) r))
     (dolist (elem- _target_positions)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;;
   self)
  )

(defclass code_llm::GetTargetPositions
  :super ros::object
  :slots ())

(setf (get code_llm::GetTargetPositions :md5sum-) "4d1569a822e71490fd292b69ce0fc339")
(setf (get code_llm::GetTargetPositions :datatype-) "code_llm/GetTargetPositions")
(setf (get code_llm::GetTargetPositions :request) code_llm::GetTargetPositionsRequest)
(setf (get code_llm::GetTargetPositions :response) code_llm::GetTargetPositionsResponse)

(defmethod code_llm::GetTargetPositionsRequest
  (:response () (instance code_llm::GetTargetPositionsResponse :init)))

(setf (get code_llm::GetTargetPositionsRequest :md5sum-) "4d1569a822e71490fd292b69ce0fc339")
(setf (get code_llm::GetTargetPositionsRequest :datatype-) "code_llm/GetTargetPositionsRequest")
(setf (get code_llm::GetTargetPositionsRequest :definition-)
      "---
ObjInfo[] target_positions


================================================================================
MSG: code_llm/ObjInfo
int32 id
string type
geometry_msgs/Point position
geometry_msgs/Twist velocity
geometry_msgs/Point target_position
float32 radius
string color

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Twist
# This expresses velocity in free space broken into its linear and angular parts.
Vector3  linear
Vector3  angular

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
")

(setf (get code_llm::GetTargetPositionsResponse :md5sum-) "4d1569a822e71490fd292b69ce0fc339")
(setf (get code_llm::GetTargetPositionsResponse :datatype-) "code_llm/GetTargetPositionsResponse")
(setf (get code_llm::GetTargetPositionsResponse :definition-)
      "---
ObjInfo[] target_positions


================================================================================
MSG: code_llm/ObjInfo
int32 id
string type
geometry_msgs/Point position
geometry_msgs/Twist velocity
geometry_msgs/Point target_position
float32 radius
string color

================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z

================================================================================
MSG: geometry_msgs/Twist
# This expresses velocity in free space broken into its linear and angular parts.
Vector3  linear
Vector3  angular

================================================================================
MSG: geometry_msgs/Vector3
# This represents a vector in free space. 
# It is only meant to represent a direction. Therefore, it does not
# make sense to apply a translation to it (e.g., when applying a 
# generic rigid transformation to a Vector3, tf2 will only apply the
# rotation). If you want your data to be translatable too, use the
# geometry_msgs/Point message instead.

float64 x
float64 y
")



(provide :code_llm/GetTargetPositions "4d1569a822e71490fd292b69ce0fc339")


