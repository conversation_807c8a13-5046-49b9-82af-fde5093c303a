;; Auto-generated. Do not edit!


(when (boundp 'code_llm::GetCharPoints)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'GetCharPoints (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::GETCHARPOINTS")
  (make-package "CODE_LLM::GETCHARPOINTS"))
(unless (find-package "CODE_LLM::GETCHARPOINTSREQUEST")
  (make-package "CODE_LLM::GETCHARPOINTSREQUEST"))
(unless (find-package "CODE_LLM::GETCHARPOINTSRESPONSE")
  (make-package "CODE_LLM::GETCHARPOINTSRESPONSE"))

(in-package "ROS")



(if (not (find-package "GEOMETRY_MSGS"))
  (ros::roseus-add-msgs "geometry_msgs"))


(defclass code_llm::GetCharPointsRequest
  :super ros::object
  :slots (_character ))

(defmethod code_llm::GetCharPointsRequest
  (:init
   (&key
    ((:character __character) "")
    )
   (send-super :init)
   (setq _character (string __character))
   self)
  (:character
   (&optional __character)
   (if __character (setq _character __character)) _character)
  (:serialization-length
   ()
   (+
    ;; string _character
    4 (length _character)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _character
       (write-long (length _character) s) (princ _character s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _character
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _character (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass code_llm::GetCharPointsResponse
  :super ros::object
  :slots (_points ))

(defmethod code_llm::GetCharPointsResponse
  (:init
   (&key
    ((:points __points) ())
    )
   (send-super :init)
   (setq _points __points)
   self)
  (:points
   (&rest __points)
   (if (keywordp (car __points))
       (send* _points __points)
     (progn
       (if __points (setq _points (car __points)))
       _points)))
  (:serialization-length
   ()
   (+
    ;; geometry_msgs/Point[] _points
    (apply #'+ (send-all _points :serialization-length)) 4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; geometry_msgs/Point[] _points
     (write-long (length _points) s)
     (dolist (elem _points)
       (send elem :serialize s)
       )
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; geometry_msgs/Point[] _points
   (let (n)
     (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4)
     (setq _points (let (r) (dotimes (i n) (push (instance geometry_msgs::Point :init) r)) r))
     (dolist (elem- _points)
     (send elem- :deserialize buf ptr-) (incf ptr- (send elem- :serialization-length))
     ))
   ;;
   self)
  )

(defclass code_llm::GetCharPoints
  :super ros::object
  :slots ())

(setf (get code_llm::GetCharPoints :md5sum-) "424d0a1ec31ac62fb6e85eff0199a4bb")
(setf (get code_llm::GetCharPoints :datatype-) "code_llm/GetCharPoints")
(setf (get code_llm::GetCharPoints :request) code_llm::GetCharPointsRequest)
(setf (get code_llm::GetCharPoints :response) code_llm::GetCharPointsResponse)

(defmethod code_llm::GetCharPointsRequest
  (:response () (instance code_llm::GetCharPointsResponse :init)))

(setf (get code_llm::GetCharPointsRequest :md5sum-) "424d0a1ec31ac62fb6e85eff0199a4bb")
(setf (get code_llm::GetCharPointsRequest :datatype-) "code_llm/GetCharPointsRequest")
(setf (get code_llm::GetCharPointsRequest :definition-)
      "string character
---
geometry_msgs/Point[] points


================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
")

(setf (get code_llm::GetCharPointsResponse :md5sum-) "424d0a1ec31ac62fb6e85eff0199a4bb")
(setf (get code_llm::GetCharPointsResponse :datatype-) "code_llm/GetCharPointsResponse")
(setf (get code_llm::GetCharPointsResponse :definition-)
      "string character
---
geometry_msgs/Point[] points


================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
")



(provide :code_llm/GetCharPoints "424d0a1ec31ac62fb6e85eff0199a4bb")


