;; Auto-generated. Do not edit!


(when (boundp 'code_llm::StopEnvironment)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'StopEnvironment (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::STOPENVIRONMENT")
  (make-package "CODE_LLM::STOPENVIRONMENT"))
(unless (find-package "CODE_LLM::STOPENVIRONMENTREQUEST")
  (make-package "CODE_LLM::STOPENVIRONMENTREQUEST"))
(unless (find-package "CODE_LLM::STOPENVIRONMENTRESPONSE")
  (make-package "CODE_LLM::STOPENVIRONMENTRESPONSE"))

(in-package "ROS")





(defclass code_llm::StopEnvironmentRequest
  :super ros::object
  :slots (_file_name ))

(defmethod code_llm::StopEnvironmentRequest
  (:init
   (&key
    ((:file_name __file_name) "")
    )
   (send-super :init)
   (setq _file_name (string __file_name))
   self)
  (:file_name
   (&optional __file_name)
   (if __file_name (setq _file_name __file_name)) _file_name)
  (:serialization-length
   ()
   (+
    ;; string _file_name
    4 (length _file_name)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _file_name
       (write-long (length _file_name) s) (princ _file_name s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _file_name
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _file_name (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass code_llm::StopEnvironmentResponse
  :super ros::object
  :slots (_success _message ))

(defmethod code_llm::StopEnvironmentResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass code_llm::StopEnvironment
  :super ros::object
  :slots ())

(setf (get code_llm::StopEnvironment :md5sum-) "e4aac7123a01c95d7b97c868c71b4525")
(setf (get code_llm::StopEnvironment :datatype-) "code_llm/StopEnvironment")
(setf (get code_llm::StopEnvironment :request) code_llm::StopEnvironmentRequest)
(setf (get code_llm::StopEnvironment :response) code_llm::StopEnvironmentResponse)

(defmethod code_llm::StopEnvironmentRequest
  (:response () (instance code_llm::StopEnvironmentResponse :init)))

(setf (get code_llm::StopEnvironmentRequest :md5sum-) "e4aac7123a01c95d7b97c868c71b4525")
(setf (get code_llm::StopEnvironmentRequest :datatype-) "code_llm/StopEnvironmentRequest")
(setf (get code_llm::StopEnvironmentRequest :definition-)
      "# StopEnvironment.srv
string file_name
---
bool success
string message

")

(setf (get code_llm::StopEnvironmentResponse :md5sum-) "e4aac7123a01c95d7b97c868c71b4525")
(setf (get code_llm::StopEnvironmentResponse :datatype-) "code_llm/StopEnvironmentResponse")
(setf (get code_llm::StopEnvironmentResponse :definition-)
      "# StopEnvironment.srv
string file_name
---
bool success
string message

")



(provide :code_llm/StopEnvironment "e4aac7123a01c95d7b97c868c71b4525")


