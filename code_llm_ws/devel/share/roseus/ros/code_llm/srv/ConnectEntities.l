;; Auto-generated. Do not edit!


(when (boundp 'code_llm::ConnectEntities)
  (if (not (find-package "CODE_LLM"))
    (make-package "CODE_LLM"))
  (shadow 'ConnectEntities (find-package "CODE_LLM")))
(unless (find-package "CODE_LLM::CONNECTENTITIES")
  (make-package "CODE_LLM::CONNECTENTITIES"))
(unless (find-package "CODE_LLM::CONNECTENTITIESREQUEST")
  (make-package "CODE_LLM::CONNECTENTITIESREQUEST"))
(unless (find-package "CODE_LLM::CONNECTENTITIESRESPONSE")
  (make-package "CODE_LLM::CONNECTENTITIESRESPONSE"))

(in-package "ROS")





(defclass code_llm::ConnectEntitiesRequest
  :super ros::object
  :slots (_self_id _target_id ))

(defmethod code_llm::ConnectEntitiesRequest
  (:init
   (&key
    ((:self_id __self_id) 0)
    ((:target_id __target_id) 0)
    )
   (send-super :init)
   (setq _self_id (round __self_id))
   (setq _target_id (round __target_id))
   self)
  (:self_id
   (&optional __self_id)
   (if __self_id (setq _self_id __self_id)) _self_id)
  (:target_id
   (&optional __target_id)
   (if __target_id (setq _target_id __target_id)) _target_id)
  (:serialization-length
   ()
   (+
    ;; int32 _self_id
    4
    ;; int32 _target_id
    4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; int32 _self_id
       (write-long _self_id s)
     ;; int32 _target_id
       (write-long _target_id s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; int32 _self_id
     (setq _self_id (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;; int32 _target_id
     (setq _target_id (sys::peek buf ptr- :integer)) (incf ptr- 4)
   ;;
   self)
  )

(defclass code_llm::ConnectEntitiesResponse
  :super ros::object
  :slots (_success ))

(defmethod code_llm::ConnectEntitiesResponse
  (:init
   (&key
    ((:success __success) nil)
    )
   (send-super :init)
   (setq _success __success)
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;;
   self)
  )

(defclass code_llm::ConnectEntities
  :super ros::object
  :slots ())

(setf (get code_llm::ConnectEntities :md5sum-) "667ef60adfe708beaa2fc0c1762188fa")
(setf (get code_llm::ConnectEntities :datatype-) "code_llm/ConnectEntities")
(setf (get code_llm::ConnectEntities :request) code_llm::ConnectEntitiesRequest)
(setf (get code_llm::ConnectEntities :response) code_llm::ConnectEntitiesResponse)

(defmethod code_llm::ConnectEntitiesRequest
  (:response () (instance code_llm::ConnectEntitiesResponse :init)))

(setf (get code_llm::ConnectEntitiesRequest :md5sum-) "667ef60adfe708beaa2fc0c1762188fa")
(setf (get code_llm::ConnectEntitiesRequest :datatype-) "code_llm/ConnectEntitiesRequest")
(setf (get code_llm::ConnectEntitiesRequest :definition-)
      "int32 self_id
int32 target_id
---
bool success

")

(setf (get code_llm::ConnectEntitiesResponse :md5sum-) "667ef60adfe708beaa2fc0c1762188fa")
(setf (get code_llm::ConnectEntitiesResponse :datatype-) "code_llm/ConnectEntitiesResponse")
(setf (get code_llm::ConnectEntitiesResponse :definition-)
      "int32 self_id
int32 target_id
---
bool success

")



(provide :code_llm/ConnectEntities "667ef60adfe708beaa2fc0c1762188fa")


