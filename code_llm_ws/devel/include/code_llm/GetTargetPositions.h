// Generated by gencpp from file code_llm/GetTargetPositions.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETTARGETPOSITIONS_H
#define CODE_LLM_MESSAGE_GETTARGETPOSITIONS_H

#include <ros/service_traits.h>


#include <code_llm/GetTargetPositionsRequest.h>
#include <code_llm/GetTargetPositionsResponse.h>


namespace code_llm
{

struct GetTargetPositions
{

typedef GetTargetPositionsRequest Request;
typedef GetTargetPositionsResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetTargetPositions
} // namespace code_llm


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::code_llm::GetTargetPositions > {
  static const char* value()
  {
    return "4d1569a822e71490fd292b69ce0fc339";
  }

  static const char* value(const ::code_llm::GetTargetPositions&) { return value(); }
};

template<>
struct DataType< ::code_llm::GetTargetPositions > {
  static const char* value()
  {
    return "code_llm/GetTargetPositions";
  }

  static const char* value(const ::code_llm::GetTargetPositions&) { return value(); }
};


// service_traits::MD5Sum< ::code_llm::GetTargetPositionsRequest> should match
// service_traits::MD5Sum< ::code_llm::GetTargetPositions >
template<>
struct MD5Sum< ::code_llm::GetTargetPositionsRequest>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::GetTargetPositions >::value();
  }
  static const char* value(const ::code_llm::GetTargetPositionsRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::GetTargetPositionsRequest> should match
// service_traits::DataType< ::code_llm::GetTargetPositions >
template<>
struct DataType< ::code_llm::GetTargetPositionsRequest>
{
  static const char* value()
  {
    return DataType< ::code_llm::GetTargetPositions >::value();
  }
  static const char* value(const ::code_llm::GetTargetPositionsRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::code_llm::GetTargetPositionsResponse> should match
// service_traits::MD5Sum< ::code_llm::GetTargetPositions >
template<>
struct MD5Sum< ::code_llm::GetTargetPositionsResponse>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::GetTargetPositions >::value();
  }
  static const char* value(const ::code_llm::GetTargetPositionsResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::GetTargetPositionsResponse> should match
// service_traits::DataType< ::code_llm::GetTargetPositions >
template<>
struct DataType< ::code_llm::GetTargetPositionsResponse>
{
  static const char* value()
  {
    return DataType< ::code_llm::GetTargetPositions >::value();
  }
  static const char* value(const ::code_llm::GetTargetPositionsResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETTARGETPOSITIONS_H
