// Generated by gencpp from file code_llm/GetTargetPositionsResponse.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETTARGETPOSITIONSRESPONSE_H
#define CODE_LLM_MESSAGE_GETTARGETPOSITIONSRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <code_llm/ObjInfo.h>

namespace code_llm
{
template <class ContainerAllocator>
struct GetTargetPositionsResponse_
{
  typedef GetTargetPositionsResponse_<ContainerAllocator> Type;

  GetTargetPositionsResponse_()
    : target_positions()  {
    }
  GetTargetPositionsResponse_(const ContainerAllocator& _alloc)
    : target_positions(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector< ::code_llm::ObjInfo_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::code_llm::ObjInfo_<ContainerAllocator> >> _target_positions_type;
  _target_positions_type target_positions;





  typedef boost::shared_ptr< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetTargetPositionsResponse_

typedef ::code_llm::GetTargetPositionsResponse_<std::allocator<void> > GetTargetPositionsResponse;

typedef boost::shared_ptr< ::code_llm::GetTargetPositionsResponse > GetTargetPositionsResponsePtr;
typedef boost::shared_ptr< ::code_llm::GetTargetPositionsResponse const> GetTargetPositionsResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator1> & lhs, const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator2> & rhs)
{
  return lhs.target_positions == rhs.target_positions;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator1> & lhs, const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4d1569a822e71490fd292b69ce0fc339";
  }

  static const char* value(const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4d1569a822e71490ULL;
  static const uint64_t static_value2 = 0xfd292b69ce0fc339ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/GetTargetPositionsResponse";
  }

  static const char* value(const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "ObjInfo[] target_positions\n"
"\n"
"\n"
"================================================================================\n"
"MSG: code_llm/ObjInfo\n"
"int32 id\n"
"string type\n"
"geometry_msgs/Point position\n"
"geometry_msgs/Twist velocity\n"
"geometry_msgs/Point target_position\n"
"float32 radius\n"
"string color\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Twist\n"
"# This expresses velocity in free space broken into its linear and angular parts.\n"
"Vector3  linear\n"
"Vector3  angular\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.target_positions);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetTargetPositionsResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::GetTargetPositionsResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::GetTargetPositionsResponse_<ContainerAllocator>& v)
  {
    s << indent << "target_positions[]" << std::endl;
    for (size_t i = 0; i < v.target_positions.size(); ++i)
    {
      s << indent << "  target_positions[" << i << "]: ";
      s << std::endl;
      s << indent;
      Printer< ::code_llm::ObjInfo_<ContainerAllocator> >::stream(s, indent + "    ", v.target_positions[i]);
    }
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETTARGETPOSITIONSRESPONSE_H
