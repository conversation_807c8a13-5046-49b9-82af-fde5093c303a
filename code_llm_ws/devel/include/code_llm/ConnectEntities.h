// Generated by gencpp from file code_llm/ConnectEntities.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_CONNECTENTITIES_H
#define CODE_LLM_MESSAGE_CONNECTENTITIES_H

#include <ros/service_traits.h>


#include <code_llm/ConnectEntitiesRequest.h>
#include <code_llm/ConnectEntitiesResponse.h>


namespace code_llm
{

struct ConnectEntities
{

typedef ConnectEntitiesRequest Request;
typedef ConnectEntitiesResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct ConnectEntities
} // namespace code_llm


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::code_llm::ConnectEntities > {
  static const char* value()
  {
    return "667ef60adfe708beaa2fc0c1762188fa";
  }

  static const char* value(const ::code_llm::ConnectEntities&) { return value(); }
};

template<>
struct DataType< ::code_llm::ConnectEntities > {
  static const char* value()
  {
    return "code_llm/ConnectEntities";
  }

  static const char* value(const ::code_llm::ConnectEntities&) { return value(); }
};


// service_traits::MD5Sum< ::code_llm::ConnectEntitiesRequest> should match
// service_traits::MD5Sum< ::code_llm::ConnectEntities >
template<>
struct MD5Sum< ::code_llm::ConnectEntitiesRequest>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::ConnectEntities >::value();
  }
  static const char* value(const ::code_llm::ConnectEntitiesRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::ConnectEntitiesRequest> should match
// service_traits::DataType< ::code_llm::ConnectEntities >
template<>
struct DataType< ::code_llm::ConnectEntitiesRequest>
{
  static const char* value()
  {
    return DataType< ::code_llm::ConnectEntities >::value();
  }
  static const char* value(const ::code_llm::ConnectEntitiesRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::code_llm::ConnectEntitiesResponse> should match
// service_traits::MD5Sum< ::code_llm::ConnectEntities >
template<>
struct MD5Sum< ::code_llm::ConnectEntitiesResponse>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::ConnectEntities >::value();
  }
  static const char* value(const ::code_llm::ConnectEntitiesResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::ConnectEntitiesResponse> should match
// service_traits::DataType< ::code_llm::ConnectEntities >
template<>
struct DataType< ::code_llm::ConnectEntitiesResponse>
{
  static const char* value()
  {
    return DataType< ::code_llm::ConnectEntities >::value();
  }
  static const char* value(const ::code_llm::ConnectEntitiesResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // CODE_LLM_MESSAGE_CONNECTENTITIES_H
