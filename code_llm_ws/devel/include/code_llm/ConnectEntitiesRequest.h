// Generated by gencpp from file code_llm/ConnectEntitiesRequest.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_CONNECTENTITIESREQUEST_H
#define CODE_LLM_MESSAGE_CONNECTENTITIESREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct ConnectEntitiesRequest_
{
  typedef ConnectEntitiesRequest_<ContainerAllocator> Type;

  ConnectEntitiesRequest_()
    : self_id(0)
    , target_id(0)  {
    }
  ConnectEntitiesRequest_(const ContainerAllocator& _alloc)
    : self_id(0)
    , target_id(0)  {
  (void)_alloc;
    }



   typedef int32_t _self_id_type;
  _self_id_type self_id;

   typedef int32_t _target_id_type;
  _target_id_type target_id;





  typedef boost::shared_ptr< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> const> ConstPtr;

}; // struct ConnectEntitiesRequest_

typedef ::code_llm::ConnectEntitiesRequest_<std::allocator<void> > ConnectEntitiesRequest;

typedef boost::shared_ptr< ::code_llm::ConnectEntitiesRequest > ConnectEntitiesRequestPtr;
typedef boost::shared_ptr< ::code_llm::ConnectEntitiesRequest const> ConnectEntitiesRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator1> & lhs, const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator2> & rhs)
{
  return lhs.self_id == rhs.self_id &&
    lhs.target_id == rhs.target_id;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator1> & lhs, const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "c73cb00f587f1eae70a53f3f05f6003d";
  }

  static const char* value(const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xc73cb00f587f1eaeULL;
  static const uint64_t static_value2 = 0x70a53f3f05f6003dULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/ConnectEntitiesRequest";
  }

  static const char* value(const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 self_id\n"
"int32 target_id\n"
;
  }

  static const char* value(const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.self_id);
      stream.next(m.target_id);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ConnectEntitiesRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::ConnectEntitiesRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::ConnectEntitiesRequest_<ContainerAllocator>& v)
  {
    s << indent << "self_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.self_id);
    s << indent << "target_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.target_id);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_CONNECTENTITIESREQUEST_H
