// Generated by gencpp from file code_llm/GetCharPointsRequest.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETCHARPOINTSREQUEST_H
#define CODE_LLM_MESSAGE_GETCHARPOINTSREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct GetCharPointsRequest_
{
  typedef GetCharPointsRequest_<ContainerAllocator> Type;

  GetCharPointsRequest_()
    : character()  {
    }
  GetCharPointsRequest_(const ContainerAllocator& _alloc)
    : character(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _character_type;
  _character_type character;





  typedef boost::shared_ptr< ::code_llm::GetCharPointsRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::GetCharPointsRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetCharPointsRequest_

typedef ::code_llm::GetCharPointsRequest_<std::allocator<void> > GetCharPointsRequest;

typedef boost::shared_ptr< ::code_llm::GetCharPointsRequest > GetCharPointsRequestPtr;
typedef boost::shared_ptr< ::code_llm::GetCharPointsRequest const> GetCharPointsRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::GetCharPointsRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::GetCharPointsRequest_<ContainerAllocator1> & lhs, const ::code_llm::GetCharPointsRequest_<ContainerAllocator2> & rhs)
{
  return lhs.character == rhs.character;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::GetCharPointsRequest_<ContainerAllocator1> & lhs, const ::code_llm::GetCharPointsRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetCharPointsRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetCharPointsRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetCharPointsRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "955075ad51d7005d324d0bd26242a6db";
  }

  static const char* value(const ::code_llm::GetCharPointsRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x955075ad51d7005dULL;
  static const uint64_t static_value2 = 0x324d0bd26242a6dbULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/GetCharPointsRequest";
  }

  static const char* value(const ::code_llm::GetCharPointsRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "string character\n"
;
  }

  static const char* value(const ::code_llm::GetCharPointsRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.character);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetCharPointsRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::GetCharPointsRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::GetCharPointsRequest_<ContainerAllocator>& v)
  {
    s << indent << "character: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.character);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETCHARPOINTSREQUEST_H
