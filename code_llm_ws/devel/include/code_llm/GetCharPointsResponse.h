// Generated by gencpp from file code_llm/GetCharPointsResponse.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETCHARPOINTSRESPONSE_H
#define CODE_LLM_MESSAGE_GETCHARPOINTSRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>

namespace code_llm
{
template <class ContainerAllocator>
struct GetCharPointsResponse_
{
  typedef GetCharPointsResponse_<ContainerAllocator> Type;

  GetCharPointsResponse_()
    : points()  {
    }
  GetCharPointsResponse_(const ContainerAllocator& _alloc)
    : points(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector< ::geometry_msgs::Point_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::geometry_msgs::Point_<ContainerAllocator> >> _points_type;
  _points_type points;





  typedef boost::shared_ptr< ::code_llm::GetCharPointsResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::GetCharPointsResponse_<ContainerAllocator> const> ConstPtr;

}; // struct GetCharPointsResponse_

typedef ::code_llm::GetCharPointsResponse_<std::allocator<void> > GetCharPointsResponse;

typedef boost::shared_ptr< ::code_llm::GetCharPointsResponse > GetCharPointsResponsePtr;
typedef boost::shared_ptr< ::code_llm::GetCharPointsResponse const> GetCharPointsResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::GetCharPointsResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::GetCharPointsResponse_<ContainerAllocator1> & lhs, const ::code_llm::GetCharPointsResponse_<ContainerAllocator2> & rhs)
{
  return lhs.points == rhs.points;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::GetCharPointsResponse_<ContainerAllocator1> & lhs, const ::code_llm::GetCharPointsResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetCharPointsResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetCharPointsResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetCharPointsResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "3fb3f9dacc279b964c4c8341122c34df";
  }

  static const char* value(const ::code_llm::GetCharPointsResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x3fb3f9dacc279b96ULL;
  static const uint64_t static_value2 = 0x4c4c8341122c34dfULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/GetCharPointsResponse";
  }

  static const char* value(const ::code_llm::GetCharPointsResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Point[] points\n"
"\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::code_llm::GetCharPointsResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.points);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetCharPointsResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::GetCharPointsResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::GetCharPointsResponse_<ContainerAllocator>& v)
  {
    s << indent << "points[]" << std::endl;
    for (size_t i = 0; i < v.points.size(); ++i)
    {
      s << indent << "  points[" << i << "]: ";
      s << std::endl;
      s << indent;
      Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "    ", v.points[i]);
    }
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETCHARPOINTSRESPONSE_H
