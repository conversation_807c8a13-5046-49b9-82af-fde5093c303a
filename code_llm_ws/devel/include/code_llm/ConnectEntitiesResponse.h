// Generated by gencpp from file code_llm/ConnectEntitiesResponse.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_CONNECTENTITIESRESPONSE_H
#define CODE_LLM_MESSAGE_CONNECTENTITIESRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct ConnectEntitiesResponse_
{
  typedef ConnectEntitiesResponse_<ContainerAllocator> Type;

  ConnectEntitiesResponse_()
    : success(false)  {
    }
  ConnectEntitiesResponse_(const ContainerAllocator& _alloc)
    : success(false)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;





  typedef boost::shared_ptr< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> const> ConstPtr;

}; // struct ConnectEntitiesResponse_

typedef ::code_llm::ConnectEntitiesResponse_<std::allocator<void> > ConnectEntitiesResponse;

typedef boost::shared_ptr< ::code_llm::ConnectEntitiesResponse > ConnectEntitiesResponsePtr;
typedef boost::shared_ptr< ::code_llm::ConnectEntitiesResponse const> ConnectEntitiesResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator1> & lhs, const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator1> & lhs, const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "358e233cde0c8a8bcfea4ce193f8fc15";
  }

  static const char* value(const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x358e233cde0c8a8bULL;
  static const uint64_t static_value2 = 0xcfea4ce193f8fc15ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/ConnectEntitiesResponse";
  }

  static const char* value(const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool success\n"
"\n"
;
  }

  static const char* value(const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ConnectEntitiesResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::ConnectEntitiesResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::ConnectEntitiesResponse_<ContainerAllocator>& v)
  {
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_CONNECTENTITIESRESPONSE_H
