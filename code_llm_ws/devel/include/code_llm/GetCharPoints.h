// Generated by gencpp from file code_llm/GetCharPoints.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETCHARPOINTS_H
#define CODE_LLM_MESSAGE_GETCHARPOINTS_H

#include <ros/service_traits.h>


#include <code_llm/GetCharPointsRequest.h>
#include <code_llm/GetCharPointsResponse.h>


namespace code_llm
{

struct GetCharPoints
{

typedef GetCharPointsRequest Request;
typedef GetCharPointsResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct GetCharPoints
} // namespace code_llm


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::code_llm::GetCharPoints > {
  static const char* value()
  {
    return "424d0a1ec31ac62fb6e85eff0199a4bb";
  }

  static const char* value(const ::code_llm::GetCharPoints&) { return value(); }
};

template<>
struct DataType< ::code_llm::GetCharPoints > {
  static const char* value()
  {
    return "code_llm/GetCharPoints";
  }

  static const char* value(const ::code_llm::GetCharPoints&) { return value(); }
};


// service_traits::MD5Sum< ::code_llm::GetCharPointsRequest> should match
// service_traits::MD5Sum< ::code_llm::GetCharPoints >
template<>
struct MD5Sum< ::code_llm::GetCharPointsRequest>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::GetCharPoints >::value();
  }
  static const char* value(const ::code_llm::GetCharPointsRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::GetCharPointsRequest> should match
// service_traits::DataType< ::code_llm::GetCharPoints >
template<>
struct DataType< ::code_llm::GetCharPointsRequest>
{
  static const char* value()
  {
    return DataType< ::code_llm::GetCharPoints >::value();
  }
  static const char* value(const ::code_llm::GetCharPointsRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::code_llm::GetCharPointsResponse> should match
// service_traits::MD5Sum< ::code_llm::GetCharPoints >
template<>
struct MD5Sum< ::code_llm::GetCharPointsResponse>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::GetCharPoints >::value();
  }
  static const char* value(const ::code_llm::GetCharPointsResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::GetCharPointsResponse> should match
// service_traits::DataType< ::code_llm::GetCharPoints >
template<>
struct DataType< ::code_llm::GetCharPointsResponse>
{
  static const char* value()
  {
    return DataType< ::code_llm::GetCharPoints >::value();
  }
  static const char* value(const ::code_llm::GetCharPointsResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETCHARPOINTS_H
