// Generated by gencpp from file code_llm/GetTargetPositionsRequest.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_GETTARGETPOSITIONSREQUEST_H
#define CODE_LLM_MESSAGE_GETTARGETPOSITIONSREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct GetTargetPositionsRequest_
{
  typedef GetTargetPositionsRequest_<ContainerAllocator> Type;

  GetTargetPositionsRequest_()
    {
    }
  GetTargetPositionsRequest_(const ContainerAllocator& _alloc)
    {
  (void)_alloc;
    }







  typedef boost::shared_ptr< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> const> ConstPtr;

}; // struct GetTargetPositionsRequest_

typedef ::code_llm::GetTargetPositionsRequest_<std::allocator<void> > GetTargetPositionsRequest;

typedef boost::shared_ptr< ::code_llm::GetTargetPositionsRequest > GetTargetPositionsRequestPtr;
typedef boost::shared_ptr< ::code_llm::GetTargetPositionsRequest const> GetTargetPositionsRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "d41d8cd98f00b204e9800998ecf8427e";
  }

  static const char* value(const ::code_llm::GetTargetPositionsRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xd41d8cd98f00b204ULL;
  static const uint64_t static_value2 = 0xe9800998ecf8427eULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/GetTargetPositionsRequest";
  }

  static const char* value(const ::code_llm::GetTargetPositionsRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "\n"
;
  }

  static const char* value(const ::code_llm::GetTargetPositionsRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream&, T)
    {}

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct GetTargetPositionsRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::GetTargetPositionsRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream&, const std::string&, const ::code_llm::GetTargetPositionsRequest_<ContainerAllocator>&)
  {}
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_GETTARGETPOSITIONSREQUEST_H
