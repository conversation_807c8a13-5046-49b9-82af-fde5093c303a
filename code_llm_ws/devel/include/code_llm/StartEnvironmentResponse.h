// Generated by gencpp from file code_llm/StartEnvironmentResponse.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_STARTENVIRONMENTRESPONSE_H
#define CODE_LLM_MESSAGE_STARTENVIRONMENTRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct StartEnvironmentResponse_
{
  typedef StartEnvironmentResponse_<ContainerAllocator> Type;

  StartEnvironmentResponse_()
    : success(false)
    , message()  {
    }
  StartEnvironmentResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;





  typedef boost::shared_ptr< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> const> ConstPtr;

}; // struct StartEnvironmentResponse_

typedef ::code_llm::StartEnvironmentResponse_<std::allocator<void> > StartEnvironmentResponse;

typedef boost::shared_ptr< ::code_llm::StartEnvironmentResponse > StartEnvironmentResponsePtr;
typedef boost::shared_ptr< ::code_llm::StartEnvironmentResponse const> StartEnvironmentResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::StartEnvironmentResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::StartEnvironmentResponse_<ContainerAllocator1> & lhs, const ::code_llm::StartEnvironmentResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::StartEnvironmentResponse_<ContainerAllocator1> & lhs, const ::code_llm::StartEnvironmentResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "937c9679a518e3a18d831e57125ea522";
  }

  static const char* value(const ::code_llm::StartEnvironmentResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x937c9679a518e3a1ULL;
  static const uint64_t static_value2 = 0x8d831e57125ea522ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/StartEnvironmentResponse";
  }

  static const char* value(const ::code_llm::StartEnvironmentResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool success\n"
"string message\n"
"\n"
;
  }

  static const char* value(const ::code_llm::StartEnvironmentResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct StartEnvironmentResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::StartEnvironmentResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::StartEnvironmentResponse_<ContainerAllocator>& v)
  {
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_STARTENVIRONMENTRESPONSE_H
