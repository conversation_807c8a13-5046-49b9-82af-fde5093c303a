// Generated by gencpp from file code_llm/ObjInfo.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_OBJINFO_H
#define CODE_LLM_MESSAGE_OBJINFO_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/Point.h>

namespace code_llm
{
template <class ContainerAllocator>
struct ObjInfo_
{
  typedef ObjInfo_<ContainerAllocator> Type;

  ObjInfo_()
    : id(0)
    , type()
    , position()
    , velocity()
    , target_position()
    , radius(0.0)
    , color()  {
    }
  ObjInfo_(const ContainerAllocator& _alloc)
    : id(0)
    , type(_alloc)
    , position(_alloc)
    , velocity(_alloc)
    , target_position(_alloc)
    , radius(0.0)
    , color(_alloc)  {
  (void)_alloc;
    }



   typedef int32_t _id_type;
  _id_type id;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _type_type;
  _type_type type;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _position_type;
  _position_type position;

   typedef  ::geometry_msgs::Twist_<ContainerAllocator>  _velocity_type;
  _velocity_type velocity;

   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _target_position_type;
  _target_position_type target_position;

   typedef float _radius_type;
  _radius_type radius;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _color_type;
  _color_type color;





  typedef boost::shared_ptr< ::code_llm::ObjInfo_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::ObjInfo_<ContainerAllocator> const> ConstPtr;

}; // struct ObjInfo_

typedef ::code_llm::ObjInfo_<std::allocator<void> > ObjInfo;

typedef boost::shared_ptr< ::code_llm::ObjInfo > ObjInfoPtr;
typedef boost::shared_ptr< ::code_llm::ObjInfo const> ObjInfoConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::ObjInfo_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::ObjInfo_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::ObjInfo_<ContainerAllocator1> & lhs, const ::code_llm::ObjInfo_<ContainerAllocator2> & rhs)
{
  return lhs.id == rhs.id &&
    lhs.type == rhs.type &&
    lhs.position == rhs.position &&
    lhs.velocity == rhs.velocity &&
    lhs.target_position == rhs.target_position &&
    lhs.radius == rhs.radius &&
    lhs.color == rhs.color;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::ObjInfo_<ContainerAllocator1> & lhs, const ::code_llm::ObjInfo_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::ObjInfo_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::ObjInfo_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ObjInfo_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::ObjInfo_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ObjInfo_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::ObjInfo_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::ObjInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "024d77e0780728e0caffdabb0af48630";
  }

  static const char* value(const ::code_llm::ObjInfo_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x024d77e0780728e0ULL;
  static const uint64_t static_value2 = 0xcaffdabb0af48630ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::ObjInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/ObjInfo";
  }

  static const char* value(const ::code_llm::ObjInfo_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::ObjInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 id\n"
"string type\n"
"geometry_msgs/Point position\n"
"geometry_msgs/Twist velocity\n"
"geometry_msgs/Point target_position\n"
"float32 radius\n"
"string color\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Twist\n"
"# This expresses velocity in free space broken into its linear and angular parts.\n"
"Vector3  linear\n"
"Vector3  angular\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::code_llm::ObjInfo_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::ObjInfo_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.id);
      stream.next(m.type);
      stream.next(m.position);
      stream.next(m.velocity);
      stream.next(m.target_position);
      stream.next(m.radius);
      stream.next(m.color);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ObjInfo_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::ObjInfo_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::ObjInfo_<ContainerAllocator>& v)
  {
    s << indent << "id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.id);
    s << indent << "type: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.type);
    s << indent << "position: ";
    s << std::endl;
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.position);
    s << indent << "velocity: ";
    s << std::endl;
    Printer< ::geometry_msgs::Twist_<ContainerAllocator> >::stream(s, indent + "  ", v.velocity);
    s << indent << "target_position: ";
    s << std::endl;
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.target_position);
    s << indent << "radius: ";
    Printer<float>::stream(s, indent + "  ", v.radius);
    s << indent << "color: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.color);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_OBJINFO_H
