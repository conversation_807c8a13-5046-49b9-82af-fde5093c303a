// Generated by gencpp from file code_llm/StartEnvironment.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_STARTENVIRONMENT_H
#define CODE_LLM_MESSAGE_STARTENVIRONMENT_H

#include <ros/service_traits.h>


#include <code_llm/StartEnvironmentRequest.h>
#include <code_llm/StartEnvironmentResponse.h>


namespace code_llm
{

struct StartEnvironment
{

typedef StartEnvironmentRequest Request;
typedef StartEnvironmentResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct StartEnvironment
} // namespace code_llm


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::code_llm::StartEnvironment > {
  static const char* value()
  {
    return "30d0efe8e416fa584926546cceee620d";
  }

  static const char* value(const ::code_llm::StartEnvironment&) { return value(); }
};

template<>
struct DataType< ::code_llm::StartEnvironment > {
  static const char* value()
  {
    return "code_llm/StartEnvironment";
  }

  static const char* value(const ::code_llm::StartEnvironment&) { return value(); }
};


// service_traits::MD5Sum< ::code_llm::StartEnvironmentRequest> should match
// service_traits::MD5Sum< ::code_llm::StartEnvironment >
template<>
struct MD5Sum< ::code_llm::StartEnvironmentRequest>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::StartEnvironment >::value();
  }
  static const char* value(const ::code_llm::StartEnvironmentRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::StartEnvironmentRequest> should match
// service_traits::DataType< ::code_llm::StartEnvironment >
template<>
struct DataType< ::code_llm::StartEnvironmentRequest>
{
  static const char* value()
  {
    return DataType< ::code_llm::StartEnvironment >::value();
  }
  static const char* value(const ::code_llm::StartEnvironmentRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::code_llm::StartEnvironmentResponse> should match
// service_traits::MD5Sum< ::code_llm::StartEnvironment >
template<>
struct MD5Sum< ::code_llm::StartEnvironmentResponse>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::StartEnvironment >::value();
  }
  static const char* value(const ::code_llm::StartEnvironmentResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::StartEnvironmentResponse> should match
// service_traits::DataType< ::code_llm::StartEnvironment >
template<>
struct DataType< ::code_llm::StartEnvironmentResponse>
{
  static const char* value()
  {
    return DataType< ::code_llm::StartEnvironment >::value();
  }
  static const char* value(const ::code_llm::StartEnvironmentResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // CODE_LLM_MESSAGE_STARTENVIRONMENT_H
