// Generated by gencpp from file code_llm/StopEnvironmentRequest.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_STOPENVIRONMENTREQUEST_H
#define CODE_LLM_MESSAGE_STOPENVIRONMENTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct StopEnvironmentRequest_
{
  typedef StopEnvironmentRequest_<ContainerAllocator> Type;

  StopEnvironmentRequest_()
    : file_name()  {
    }
  StopEnvironmentRequest_(const ContainerAllocator& _alloc)
    : file_name(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _file_name_type;
  _file_name_type file_name;





  typedef boost::shared_ptr< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> const> ConstPtr;

}; // struct StopEnvironmentRequest_

typedef ::code_llm::StopEnvironmentRequest_<std::allocator<void> > StopEnvironmentRequest;

typedef boost::shared_ptr< ::code_llm::StopEnvironmentRequest > StopEnvironmentRequestPtr;
typedef boost::shared_ptr< ::code_llm::StopEnvironmentRequest const> StopEnvironmentRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::StopEnvironmentRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::StopEnvironmentRequest_<ContainerAllocator1> & lhs, const ::code_llm::StopEnvironmentRequest_<ContainerAllocator2> & rhs)
{
  return lhs.file_name == rhs.file_name;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::StopEnvironmentRequest_<ContainerAllocator1> & lhs, const ::code_llm::StopEnvironmentRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2415261c9605b9f38867ffbbe495fc04";
  }

  static const char* value(const ::code_llm::StopEnvironmentRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2415261c9605b9f3ULL;
  static const uint64_t static_value2 = 0x8867ffbbe495fc04ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/StopEnvironmentRequest";
  }

  static const char* value(const ::code_llm::StopEnvironmentRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# StopEnvironment.srv\n"
"string file_name\n"
;
  }

  static const char* value(const ::code_llm::StopEnvironmentRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.file_name);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct StopEnvironmentRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::StopEnvironmentRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::StopEnvironmentRequest_<ContainerAllocator>& v)
  {
    s << indent << "file_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.file_name);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_STOPENVIRONMENTREQUEST_H
