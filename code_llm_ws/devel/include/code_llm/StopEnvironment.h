// Generated by gencpp from file code_llm/StopEnvironment.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_STOPENVIRONMENT_H
#define CODE_LLM_MESSAGE_STOPENVIRONMENT_H

#include <ros/service_traits.h>


#include <code_llm/StopEnvironmentRequest.h>
#include <code_llm/StopEnvironmentResponse.h>


namespace code_llm
{

struct StopEnvironment
{

typedef StopEnvironmentRequest Request;
typedef StopEnvironmentResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct StopEnvironment
} // namespace code_llm


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::code_llm::StopEnvironment > {
  static const char* value()
  {
    return "e4aac7123a01c95d7b97c868c71b4525";
  }

  static const char* value(const ::code_llm::StopEnvironment&) { return value(); }
};

template<>
struct DataType< ::code_llm::StopEnvironment > {
  static const char* value()
  {
    return "code_llm/StopEnvironment";
  }

  static const char* value(const ::code_llm::StopEnvironment&) { return value(); }
};


// service_traits::MD5Sum< ::code_llm::StopEnvironmentRequest> should match
// service_traits::MD5Sum< ::code_llm::StopEnvironment >
template<>
struct MD5Sum< ::code_llm::StopEnvironmentRequest>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::StopEnvironment >::value();
  }
  static const char* value(const ::code_llm::StopEnvironmentRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::StopEnvironmentRequest> should match
// service_traits::DataType< ::code_llm::StopEnvironment >
template<>
struct DataType< ::code_llm::StopEnvironmentRequest>
{
  static const char* value()
  {
    return DataType< ::code_llm::StopEnvironment >::value();
  }
  static const char* value(const ::code_llm::StopEnvironmentRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::code_llm::StopEnvironmentResponse> should match
// service_traits::MD5Sum< ::code_llm::StopEnvironment >
template<>
struct MD5Sum< ::code_llm::StopEnvironmentResponse>
{
  static const char* value()
  {
    return MD5Sum< ::code_llm::StopEnvironment >::value();
  }
  static const char* value(const ::code_llm::StopEnvironmentResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::code_llm::StopEnvironmentResponse> should match
// service_traits::DataType< ::code_llm::StopEnvironment >
template<>
struct DataType< ::code_llm::StopEnvironmentResponse>
{
  static const char* value()
  {
    return DataType< ::code_llm::StopEnvironment >::value();
  }
  static const char* value(const ::code_llm::StopEnvironmentResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // CODE_LLM_MESSAGE_STOPENVIRONMENT_H
