// Generated by gencpp from file code_llm/Observations.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_OBSERVATIONS_H
#define CODE_LLM_MESSAGE_OBSERVATIONS_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <code_llm/ObjInfo.h>

namespace code_llm
{
template <class ContainerAllocator>
struct Observations_
{
  typedef Observations_<ContainerAllocator> Type;

  Observations_()
    : observations()  {
    }
  Observations_(const ContainerAllocator& _alloc)
    : observations(_alloc)  {
  (void)_alloc;
    }



   typedef std::vector< ::code_llm::ObjInfo_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::code_llm::ObjInfo_<ContainerAllocator> >> _observations_type;
  _observations_type observations;





  typedef boost::shared_ptr< ::code_llm::Observations_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::Observations_<ContainerAllocator> const> ConstPtr;

}; // struct Observations_

typedef ::code_llm::Observations_<std::allocator<void> > Observations;

typedef boost::shared_ptr< ::code_llm::Observations > ObservationsPtr;
typedef boost::shared_ptr< ::code_llm::Observations const> ObservationsConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::Observations_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::Observations_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::Observations_<ContainerAllocator1> & lhs, const ::code_llm::Observations_<ContainerAllocator2> & rhs)
{
  return lhs.observations == rhs.observations;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::Observations_<ContainerAllocator1> & lhs, const ::code_llm::Observations_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::Observations_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::Observations_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::Observations_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::Observations_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::Observations_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::Observations_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::Observations_<ContainerAllocator> >
{
  static const char* value()
  {
    return "2c2d592d2da77cd414a280b0f81875a8";
  }

  static const char* value(const ::code_llm::Observations_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x2c2d592d2da77cd4ULL;
  static const uint64_t static_value2 = 0x14a280b0f81875a8ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::Observations_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/Observations";
  }

  static const char* value(const ::code_llm::Observations_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::Observations_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 存放单个机器人所有的感知信息\n"
"\n"
"ObjInfo[] observations\n"
"\n"
"================================================================================\n"
"MSG: code_llm/ObjInfo\n"
"int32 id\n"
"string type\n"
"geometry_msgs/Point position\n"
"geometry_msgs/Twist velocity\n"
"geometry_msgs/Point target_position\n"
"float32 radius\n"
"string color\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Twist\n"
"# This expresses velocity in free space broken into its linear and angular parts.\n"
"Vector3  linear\n"
"Vector3  angular\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Vector3\n"
"# This represents a vector in free space. \n"
"# It is only meant to represent a direction. Therefore, it does not\n"
"# make sense to apply a translation to it (e.g., when applying a \n"
"# generic rigid transformation to a Vector3, tf2 will only apply the\n"
"# rotation). If you want your data to be translatable too, use the\n"
"# geometry_msgs/Point message instead.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::code_llm::Observations_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::Observations_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.observations);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct Observations_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::Observations_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::Observations_<ContainerAllocator>& v)
  {
    s << indent << "observations[]" << std::endl;
    for (size_t i = 0; i < v.observations.size(); ++i)
    {
      s << indent << "  observations[" << i << "]: ";
      s << std::endl;
      s << indent;
      Printer< ::code_llm::ObjInfo_<ContainerAllocator> >::stream(s, indent + "    ", v.observations[i]);
    }
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_OBSERVATIONS_H
