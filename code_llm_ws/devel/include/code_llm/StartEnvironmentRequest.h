// Generated by gencpp from file code_llm/StartEnvironmentRequest.msg
// DO NOT EDIT!


#ifndef CODE_LLM_MESSAGE_STARTENVIRONMENTREQUEST_H
#define CODE_LLM_MESSAGE_STARTENVIRONMENTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace code_llm
{
template <class ContainerAllocator>
struct StartEnvironmentRequest_
{
  typedef StartEnvironmentRequest_<ContainerAllocator> Type;

  StartEnvironmentRequest_()
    : experiment_path()  {
    }
  StartEnvironmentRequest_(const ContainerAllocator& _alloc)
    : experiment_path(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _experiment_path_type;
  _experiment_path_type experiment_path;





  typedef boost::shared_ptr< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> const> ConstPtr;

}; // struct StartEnvironmentRequest_

typedef ::code_llm::StartEnvironmentRequest_<std::allocator<void> > StartEnvironmentRequest;

typedef boost::shared_ptr< ::code_llm::StartEnvironmentRequest > StartEnvironmentRequestPtr;
typedef boost::shared_ptr< ::code_llm::StartEnvironmentRequest const> StartEnvironmentRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::code_llm::StartEnvironmentRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::code_llm::StartEnvironmentRequest_<ContainerAllocator1> & lhs, const ::code_llm::StartEnvironmentRequest_<ContainerAllocator2> & rhs)
{
  return lhs.experiment_path == rhs.experiment_path;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::code_llm::StartEnvironmentRequest_<ContainerAllocator1> & lhs, const ::code_llm::StartEnvironmentRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace code_llm

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "22169514ab4c983fbbbb1dd2d9401961";
  }

  static const char* value(const ::code_llm::StartEnvironmentRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x22169514ab4c983fULL;
  static const uint64_t static_value2 = 0xbbbb1dd2d9401961ULL;
};

template<class ContainerAllocator>
struct DataType< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "code_llm/StartEnvironmentRequest";
  }

  static const char* value(const ::code_llm::StartEnvironmentRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# StartEnvironment.srv\n"
"string experiment_path\n"
;
  }

  static const char* value(const ::code_llm::StartEnvironmentRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.experiment_path);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct StartEnvironmentRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::code_llm::StartEnvironmentRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::code_llm::StartEnvironmentRequest_<ContainerAllocator>& v)
  {
    s << indent << "experiment_path: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.experiment_path);
  }
};

} // namespace message_operations
} // namespace ros

#endif // CODE_LLM_MESSAGE_STARTENVIRONMENTREQUEST_H
